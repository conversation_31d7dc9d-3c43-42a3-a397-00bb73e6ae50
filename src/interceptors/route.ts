/**
 * 路由拦截器 - 简化版本
 * 移除登录检查，允许所有页面正常访问
 * 登录状态由各个功能点自行处理
 */

// 简化的路由拦截器，不做任何登录检查
const navigateToInterceptor = {
  invoke({ url }: { url: string }) {
    console.log('页面导航:', url)
    // 允许所有页面正常访问
    return true
  },
}

export const routeInterceptor = {
  install() {
    uni.addInterceptor('navigateTo', navigateToInterceptor)
    uni.addInterceptor('reLaunch', navigateToInterceptor)
    uni.addInterceptor('redirectTo', navigateToInterceptor)
  },
}
