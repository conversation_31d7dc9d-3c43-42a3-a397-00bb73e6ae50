/**
 * 导航相关工具函数
 */
import { tabBar } from '@/pages.json'

/**
 * 获取当前页面
 * @returns 当前页面对象
 */
export const getCurrentPage = () => {
  const pages = getCurrentPages()
  return pages[pages.length - 1]
}

/**
 * 判断当前页面是否是 tabBar 页面
 * @returns 是否是 tabBar 页面
 */
export const isTabBarPage = () => {
  if (!tabBar || !tabBar.list || !tabBar.list.length) {
    return false
  }

  const currentPage = getCurrentPage()
  const currentPath = currentPage.route

  return !!tabBar.list.find((item) => item.pagePath === currentPath)
}

/**
 * 判断是否是从 tabBar 切换过来的
 * 以下情况视为从 tabBar 切换过来：
 * 1. 当前页面栈只有一个页面（应用刚启动）
 * 2. 当前页面是 tabBar 页面
 * @returns 是否是从 tabBar 切换过来
 */
export const isFromTabBar = () => {
  const pages = getCurrentPages()

  // 如果页面栈只有一个页面，说明是应用刚启动
  if (pages.length === 1) {
    return true
  }

  // 如果当前页面是 tabBar 页面，说明是从 tabBar 切换过来
  return isTabBarPage()
}

/**
 * 判断是否需要刷新数据
 * 以下情况需要刷新数据：
 * 1. 从 tabBar 切换过来
 * 2. 有刷新标记
 * 3. 数据过期（超过刷新时间间隔）
 * @param needRefresh 是否有刷新标记
 * @param isDataStale 数据是否过期
 * @returns 是否需要刷新数据
 */
export const shouldRefreshData = (needRefresh: boolean, isDataStale: boolean) => {
  return isFromTabBar() || needRefresh || isDataStale
}
