/**
 * 标签颜色工具函数
 * 根据标签内容或索引生成对应的背景色和文字颜色
 */

// 标签背景色数组 - 从图片中提取的柔和色彩
const TAG_COLORS = [
  { bgColor: '#E7F3E8', textColor: '#448361' }, // 浅绿色
  { bgColor: '#FCE8E9', textColor: '#D44C47' }, // 浅红色
  { bgColor: '#E7F3F8', textColor: '#347EA9' }, // 浅蓝色
  { bgColor: '#F9E8F0', textColor: '#9C4668' }, // 浅粉色
  { bgColor: '#F0E7F8', textColor: '#7E4A9C' }, // 浅紫色
  { bgColor: '#FAF1D8', textColor: '#CB912F' }, // 浅黄色
  { bgColor: '#F0F0F0', textColor: '#666666' }, // 浅灰色
  { bgColor: '#E8F0F9', textColor: '#4A6B9C' }, // 浅蓝灰色
  { bgColor: '#E8F9F3', textColor: '#3A9C7E' }, // 浅青色
  { bgColor: '#F9F0E8', textColor: '#9C6B4A' }, // 浅棕色
]

/**
 * 根据标签内容获取颜色
 * @param tag 标签文本
 * @param index 标签索引（可选）
 * @returns 包含背景色和文字颜色的对象
 */
export function getTagColorByContent(tag: string, index?: number): { bgColor: string; textColor: string } {
  // 如果提供了索引，直接使用索引取模获取颜色
  if (index !== undefined) {
    return TAG_COLORS[index % TAG_COLORS.length]
  }
  
  // 根据标签内容的字符编码总和计算索引
  let sum = 0
  for (let i = 0; i < tag.length; i++) {
    sum += tag.charCodeAt(i)
  }
  
  return TAG_COLORS[sum % TAG_COLORS.length]
}

/**
 * 获取所有可用的标签颜色
 * @returns 标签颜色数组
 */
export function getAllTagColors(): Array<{ bgColor: string; textColor: string }> {
  return TAG_COLORS
}
