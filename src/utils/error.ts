/**
 * 错误处理工具函数
 * 提供统一的错误信息提取和处理方法
 */

/**
 * 从错误对象中提取错误信息
 * @param error 错误对象，可以是任意类型
 * @param defaultMessage 默认错误信息，当无法从错误对象中提取信息时使用
 * @returns 提取的错误信息字符串
 */
export const extractErrorMessage = (error: any, defaultMessage: string = '未知错误'): string => {
  // 如果错误为空，返回默认错误信息
  if (error === null || error === undefined) {
    return defaultMessage
  }

  // 如果错误是字符串，直接返回
  if (typeof error === 'string') {
    return error
  }

  // 如果错误是对象，尝试提取错误信息
  if (typeof error === 'object') {
    // 按优先级尝试不同的错误信息字段
    return (
      error.message || // 标准 Error 对象的 message 字段
      error.msg || // 自定义错误对象或 API 响应的 msg 字段
      error.errMsg || // uni-app 错误对象的 errMsg 字段
      error.error || // 某些库可能使用 error 字段
      error.errorMessage || // 某些库可能使用 errorMessage 字段
      (error.data && (typeof error.data === 'object' ? error.data.msg : error.data)) || // 嵌套在 data 中的错误信息
      JSON.stringify(error) // 如果没有找到特定字段，将整个对象转为字符串
    )
  }

  // 其他类型，转为字符串
  return String(error)
}

/**
 * 格式化错误信息，添加前缀
 * @param error 错误对象
 * @param prefix 错误信息前缀
 * @param defaultMessage 默认错误信息
 * @returns 格式化后的错误信息
 */
export const formatErrorMessage = (
  error: any,
  prefix: string = '',
  defaultMessage: string = '未知错误',
): string => {
  const errorMsg = extractErrorMessage(error, defaultMessage)
  return prefix ? `${prefix}: ${errorMsg}` : errorMsg
}
