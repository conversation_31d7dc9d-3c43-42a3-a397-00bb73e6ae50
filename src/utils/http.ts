import { CustomRequestOptions } from '@/interceptors/request'
import type { IResData } from '@/typings'
import { isLoginInvalidCode } from '@/constants/errorCode'
import { useUserStore } from '@/store'
import { getEnvBaseUrl } from '@/utils'
import { wxLoginApi } from '@/service/user/auth'
import { getUserInfo } from '@/service/user/user'
import { DEFAULT_AVATAR } from '@/constants/common'

// 正在重新登录的Promise，避免并发请求时重复登录
let reloginPromise: Promise<boolean> | null = null

/**
 * 自动静默重新登录
 * @returns Promise<boolean> 登录是否成功
 */
const autoSilentRelogin = async (): Promise<boolean> => {
  // 如果已经在重新登录中，等待结果
  if (reloginPromise) {
    return reloginPromise
  }

  reloginPromise = (async () => {
    try {
      console.log('开始自动静默重新登录')

      // 获取微信登录凭证
      const { code } = await uni.login({ provider: 'weixin' })
      console.log('获取到微信登录 code:', code)

      // 调用登录API
      const { data } = await wxLoginApi(code)
      console.log('静默重新登录结果:', data)

      const userStore = useUserStore()

      if (data) {
        if (typeof data === 'object') {
          // 如果返回的是用户信息对象
          userStore.setUserInfo(data)
          console.log('用户信息已更新:', data)
        } else if (typeof data === 'string') {
          // 如果只返回了token
          userStore.setToken(data)
          console.log('用户Token已更新:', data)

          // 异步获取用户详细信息
          try {
            const userInfoRes = await getUserInfo()
            if (userInfoRes.data) {
              const mergedUserInfo = { ...userStore.userInfo, ...userInfoRes.data }
              if (!mergedUserInfo.avatar || mergedUserInfo.avatar.startsWith('wxfile')) {
                mergedUserInfo.avatar = DEFAULT_AVATAR
              }
              userStore.setUserInfo(mergedUserInfo)
              console.log('用户详细信息已更新:', mergedUserInfo)
            }
          } catch (error) {
            console.error('获取用户详细信息失败:', error)
          }
        }

        console.log('自动静默重新登录成功')
        return true
      } else {
        console.error('自动静默重新登录失败: 服务器返回空数据')
        return false
      }
    } catch (error) {
      console.error('自动静默重新登录失败:', error)
      return false
    } finally {
      // 清除重新登录Promise
      reloginPromise = null
    }
  })()

  return reloginPromise
}

export const http = <T>(options: CustomRequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<IResData<T>>((resolve, reject) => {
    const makeRequest = () => {
      uni.request({
        ...options,
        // 响应成功
        success(res) {
          console.log('请求成功:', options.url, res.statusCode, res.data)
          // 状态码 2xx，参考 axios 的设计
          if (res.statusCode >= 200 && res.statusCode < 300) {
            // 2.1 提取核心数据 res.data
            // 确保返回的数据符合 IResData 结构
            try {
              const responseData = res.data as IResData<T>
              // 验证关键字段是否存在
              if (responseData.code === undefined) {
                console.warn('响应数据缺少 code 字段')
              }

              // 检查是否是登录失效的错误码
              if (responseData.code !== undefined && isLoginInvalidCode(responseData.code)) {
                console.warn('登录信息已失效，错误码:', responseData.code)
                // 自动进行静默重新登录
                handleLoginInvalidAutoRelogin(responseData.msg || '登录已失效')
                  .then((success) => {
                    if (success) {
                      // 重新登录成功，重试原请求
                      console.log('重新登录成功，重试原请求')
                      makeRequest()
                    } else {
                      // 重新登录失败，返回原错误
                      console.log('重新登录失败，返回原错误')
                      reject(responseData)
                    }
                  })
                  .catch((error) => {
                    console.error('处理登录失效时发生错误:', error)
                    reject(responseData)
                  })
                return
              }

              resolve(responseData)
            } catch (error) {
              console.error('解析响应数据失败:', error)
              reject(new Error('响应数据格式不正确'))
            }
          } else if (res.statusCode === 401) {
            // 401错误 -> 未授权，也尝试自动重新登录
            console.log('收到401错误，尝试自动重新登录')
            handleLoginInvalidAutoRelogin('登录已过期')
              .then((success) => {
                if (success) {
                  console.log('重新登录成功，重试原请求')
                  makeRequest()
                } else {
                  console.log('重新登录失败')
                  reject(res)
                }
              })
              .catch((error) => {
                console.error('处理401错误时发生错误:', error)
                reject(res)
              })
          } else {
            // 其他错误 -> 根据后端错误信息轻提示
            let errorMsg = '请求错误'
            try {
              if (res.data && typeof res.data === 'object' && 'msg' in res.data) {
                errorMsg = res.data.msg || errorMsg
              }
            } catch (e) {
              console.error('获取错误信息失败:', e)
            }
            !options.hideErrorToast &&
              uni.showToast({
                icon: 'none',
                title: errorMsg,
              })
            reject(res)
          }
        },
        // 响应失败
        fail(err) {
          console.error('请求失败:', options.url, err)
          // 网络错误
          !options.hideErrorToast &&
            uni.showToast({
              icon: 'none',
              title: '网络错误，请检查网络连接',
            })
          reject(err)
        },
      })
    }

    // 开始请求
    makeRequest()
  })
}

/**
 * 处理登录失效 - 自动静默重新登录
 * @param errorMsg 错误信息
 * @returns Promise<boolean> 重新登录是否成功
 */
const handleLoginInvalidAutoRelogin = async (errorMsg: string): Promise<boolean> => {
  console.log('处理登录失效，开始自动静默重新登录:', errorMsg)

  // 获取用户存储
  const userStore = useUserStore()

  // 清除用户信息
  userStore.clearUserInfo()

  // 进行自动静默重新登录
  const success = await autoSilentRelogin()

  if (success) {
    console.log('自动静默重新登录成功')
  } else {
    console.log('自动静默重新登录失败')
  }

  return success
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @returns
 */
export const httpGet = <T>(url: string, query?: Record<string, any>) => {
  return http<T>({
    url,
    query,
    method: 'GET',
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @returns
 */
export const httpPost = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
  })
}

/**
 * 文件上传请求
 * @param url 后台地址
 * @param filePath 文件路径
 * @param formData 额外的表单数据
 * @returns Promise
 */
export const httpUploadFile = <T>(
  url: string,
  filePath: string,
  formData?: Record<string, any>,
) => {
  // 获取基础URL
  const baseUrl = getEnvBaseUrl()

  // 构建完整URL
  const fullUrl = url.startsWith('http') ? url : baseUrl + url

  // 返回Promise
  return new Promise<IResData<T>>((resolve, reject) => {
    uni.uploadFile({
      url: fullUrl,
      filePath,
      name: 'file', // 文件对应的 key，开发者在服务端可以通过这个 key 获取文件的二进制内容
      formData,
      success: (res) => {
        console.log('上传成功:', url, res.statusCode, res.data)
        // 状态码 2xx
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            // 解析返回的JSON数据
            const responseData = JSON.parse(res.data) as IResData<T>

            // 验证关键字段是否存在
            if (responseData.code === undefined) {
              console.warn('响应数据缺少 code 字段')
            }

            // 检查是否是登录失效的错误码
            if (responseData.code !== undefined && isLoginInvalidCode(responseData.code)) {
              console.warn('登录信息已失效，错误码:', responseData.code)
              // 处理登录失效情况
              handleLoginInvalidAutoRelogin(responseData.msg || '登录已失效')
                .then((success) => {
                  if (success) {
                    // 重新登录成功，重试原请求
                    console.log('重新登录成功，重试原请求')
                    resolve(responseData)
                  } else {
                    // 重新登录失败，返回原错误
                    console.log('重新登录失败，返回原错误')
                    reject(responseData)
                  }
                })
                .catch((error) => {
                  console.error('处理登录失效时发生错误:', error)
                  reject(responseData)
                })
              return
            }

            resolve(responseData)
          } catch (error) {
            console.error('解析响应数据失败:', error)
            reject(new Error('响应数据格式不正确'))
          }
        } else if (res.statusCode === 401) {
          // 401错误 -> 未授权
          reject(res)
        } else {
          // 其他错误
          let errorMsg = '上传失败'
          try {
            const data = JSON.parse(res.data)
            if (data && typeof data === 'object' && 'msg' in data) {
              errorMsg = data.msg || errorMsg
            }
          } catch (e) {
            console.error('获取错误信息失败:', e)
          }

          uni.showToast({
            icon: 'none',
            title: errorMsg,
          })
          reject(res)
        }
      },
      fail: (err) => {
        console.error('上传失败:', url, err)
        // 网络错误
        uni.showToast({
          icon: 'none',
          title: '网络错误，请检查网络连接',
        })
        reject(err)
      },
    })
  })
}

http.get = httpGet
http.post = httpPost
http.uploadFile = httpUploadFile
