{"name": "NotionMpClipper", "appid": "__UNI__212111F", "description": "", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": []}, "ios": {}, "sdkConfigs": {}}}, "quickapp": {}, "mp-weixin": {"appid": "wxa2abb91f64032a2b", "setting": {"urlCheck": false}, "usingComponents": true, "supportedMaterials": [{"materialType": "text/html", "name": "用${nickname}打开", "desc": "同步文章到Notion", "path": "pages/index/article/article"}]}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "locale": "zh-Hans", "h5": {"router": {"base": "/theroad/"}}}