/**
 * 错误码常量
 */
export const ErrorCode = {
  // 登录相关错误码
  TOKEN_EXPIRED: 80001, // token过期
  TOKEN_INVALID: 80000, // token无效
  SESSION_EXPIRED: 80005, // 会话过期
  UNAUTHORIZED: 80006, // 未授权
}

/**
 * 判断是否是登录失效的错误码
 * @param code 错误码
 * @returns 是否是登录失效的错误码
 */
export const isLoginInvalidCode = (code: number): boolean => {
  return [
    ErrorCode.TOKEN_EXPIRED,
    ErrorCode.TOKEN_INVALID,
    ErrorCode.SESSION_EXPIRED,
    ErrorCode.UNAUTHORIZED,
  ].includes(code)
}
