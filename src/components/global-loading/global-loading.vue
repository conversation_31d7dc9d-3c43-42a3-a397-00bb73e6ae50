<template>
  <view v-if="show" class="global-loading-overlay">
    <view class="loading-container">
      <view class="app-icon">
        <image src="/static/app/icons/120x120.png" class="icon-image" />
      </view>
      <view class="loading-spinner">
        <view class="spinner-dot" />
        <view class="spinner-dot" />
        <view class="spinner-dot" />
      </view>
      <view class="loading-text">{{ loadingText }}</view>
      <view class="loading-tip">正在为您准备最佳体验...</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/store'

interface Props {
  show?: boolean
  text?: string
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  text: '加载中',
})

const userStore = useUserStore()

const loadingText = computed(() => {
  if (userStore.isLoginLoading) {
    return '正在登录...'
  }
  if (userStore.isAppInitializing) {
    return '正在初始化...'
  }
  return props.text
})
</script>

<style lang="scss" scoped>
.global-loading-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.app-icon {
  margin-bottom: 40rpx;
}

.icon-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  display: flex;
  gap: 8rpx;
  margin-bottom: 32rpx;
}

.spinner-dot {
  width: 12rpx;
  height: 12rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.spinner-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.spinner-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-text {
  margin-bottom: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: white;
  opacity: 0.9;
}

.loading-tip {
  max-width: 500rpx;
  font-size: 24rpx;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.7);
}

@keyframes loading-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
