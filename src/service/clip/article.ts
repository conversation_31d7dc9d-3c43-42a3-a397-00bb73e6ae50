import { http } from '@/utils/http'

export interface ArticleBody {
  title: string
  link: string
  author: string
  origin: string
  tags: string[]
  remark: string
  publishTime: string
  createTime: string
  cover: string
  icon: string
  siteName: string
  description: string
}

export interface UrlRQ {
  url: string
}

/**
 * 解析链接
 */
export const resolveLink = (link: UrlRQ) => {
  return http.post<ArticleBody>('/miniprogram/resolve', link)
}

/**
 * 保存文章
 */
export const saveArticle = (content: ArticleBody) => {
  return http.post<boolean>('/miniprogram/submit', content)
}
