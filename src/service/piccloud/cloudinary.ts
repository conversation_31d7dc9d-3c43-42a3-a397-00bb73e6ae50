import { http } from '@/utils/http'

export interface Cloudinary {
  cloudName: string
  apiKey: string
  apiSecret: string
}

/**
 * 获取用户是否绑定了Cloudinary
 */
export const getHasBindCloudinary = () => {
  return http.get<string>('/miniprogram/getHasBindCloudinary')
}

export const getCloudinaryConfig = () => {
  return http.get<Cloudinary>('/miniprogram/getCloudinaryConfigV2')
}

export const postCloudinaryConfig = (body: Cloudinary) => {
  return http.post<boolean>('/miniprogram/postCloudinaryConfig', body)
}

export const removeCloudinaryConfig = () => {
  return http.get<boolean>('/miniprogram/removeCloudinaryConfig')
}
