import { http } from '@/utils/http'

export interface S3Config {
  type: string
  endpoint: string
  accessKey: string
  accessSecret: string
  bucketName: string
  region: string
  customDomain: string
}

/**
 * 查询用户的 S3 配置
 */
export const getS3Config = () => {
  return http.get<S3Config>('/miniprogram/getOssConfig')
}

/**
 * 保存 S3 配置
 */
export const saveS3Config = (body: S3Config) => {
  return http.post<boolean>('/miniprogram/postOssConfig', body)
}

export const removeS3Config = () => {
  return http.get<boolean>('/miniprogram/removeOssConfig')
}
