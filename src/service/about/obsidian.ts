import { http } from '@/utils/http'

/**
 * Obsidian 配置接口
 */
export interface ObsidianConfig {
  /**
   * 主键 id
   */
  id?: number

  /**
   * 用户id
   */
  unionId?: string

  /**
   * 文章属性配置
   */
  artInfo?: string

  /**
   * 消息属性配置
   */
  msgInfo?: string

  /**
   * s3配置id
   */
  s3Id?: number

  /**
   * 文章保存路径
   */
  saveRoot?: string

  /**
   * 资源保存路径
   */
  attRoot?: string

  /**
   * 创建时间戳
   */
  gmtCreate?: number

  /**
   * 更新时间戳
   */
  gmtUpdate?: number

  /**
   * 是否删除
   */
  deleted?: number
}

/**
 * 文章属性配置
 */
export interface ArticleConfig {
  category: string
  title: string
  url: string
  author: string
  tags: string
  origin: string
  remark: string
  createTime: string
  publishTime: string
}

/**
 * 消息属性配置
 */
export interface MessageConfig {
  category: string
  tags: string
  createTime: string
}

/**
 * 获取 Obsidian 配置
 */
export const getObsidianConfig = () => {
  return http.get<ObsidianConfig>('/miniprogram/obsidian/getConfig')
}

/**
 * 保存 Obsidian 配置
 */
export const saveObsidianConfig = (config: ObsidianConfig) => {
  return http.post<boolean>('/miniprogram/obsidian/saveConfig', config)
}
