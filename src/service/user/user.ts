import { http } from '@/utils/http'
import type { IUserInfo } from '@/typings'

export interface UserSimpleInfo {
  unionId: string
  nickname: string
  avatar: string
  email: string
  subUserId: string
}

export interface UserData {
  msgCount: number
  articleCount: number
  shareCount: number
}

export interface UserClipData {
  curDayCount: number
  curMonthCount: number
  totalCount: number
}

/**
 * 获取用户信息
 */
export const getUserInfo = () => {
  return http.get<IUserInfo>('/miniprogram/getUserInfo')
}

/**
 * 获取用户数据
 */
export const getUserData = () => {
  return http.get<UserData>('/miniprogram/getUserData')
}

/**
 * 获取用户剪藏数据
 */
export const getUserClipData = () => {
  return http.get<UserClipData>('/miniprogram/getUserClipData')
}

/**
 * 更新用户信息
 */
export const updateUserInfo = (data: UserSimpleInfo) => {
  return http.post<boolean>('/miniprogram/updateUserInfo', data)
}

/**
 * 上传用户头像
 * @param filePath 头像文件路径（一般是临时文件路径）
 * @param unionId 用户的 unionId
 * @returns 返回上传后的头像 URL
 */
export const uploadAvatar = (filePath: string, unionId: string) => {
  return http.uploadFile<string>('/miniprogram/image/avatar/upload', filePath, { unionId })
}
