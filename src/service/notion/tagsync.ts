import { http } from '@/utils/http'

/**
 * Notion 标签字段信息返回类型
 */
export interface NotionTagFieldResponse {
  fieldName: string // 字段名称，如 "Tags"
  fieldType: string // 字段类型，如 "multi_select"
  options: string[] // 标签选项列表，直接是字符串数组
}

/**
 * 用户标签备份请求类型
 */
export interface UserTagBackupRequest {
  tags: string[] // 本地标签列表
}

/**
 * 将本地标签同步备份到服务器
 * 接口返回的 data 字段是 boolean 类型，表示操作是否成功
 */
export const syncUserCommonTagsAPI = (tags: string[]) => {
  console.log('开始备份本地标签到服务器，标签数量:', tags.length)
  const requestData: UserTagBackupRequest = { tags }
  return http.post<boolean>('/miniprogram/notion/syncUserCommonTags', requestData)
}

/**
 * 从 Notion 文章数据库获取标签选项（最多100个）
 * @returns Promise<NotionTagFieldResponse>
 */
export const getNotionArticleDatabaseTagOptionsAPI = () => {
  console.log('开始从 Notion 获取标签选项')
  return http.get<NotionTagFieldResponse>('/miniprogram/notion/getArticleDatabaseTagOptions')
}

/**
 * 删除服务器上的标签备份
 * 接口返回的 data 字段是 boolean 类型，表示操作是否成功
 */
export const deleteUserCommonTagsAPI = () => {
  console.log('开始删除服务器上的标签备份')
  return http.post<boolean>('/miniprogram/notion/deleteCommonTags')
}
