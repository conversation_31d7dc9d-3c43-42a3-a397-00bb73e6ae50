import { http } from '@/utils/http'

/**
 * 文章数据库字段映射返回类型
 */
export interface ArticleFieldDTO {
  databaseId: string // Notion数据库ID
  url: Field
  author: Field
  origin: Field
  tags: Field
  remark: Field
  publishTime: Field
  createTime: Field
}

export interface MessageFieldDTO {
  databaseId: string // Notion数据库ID
  tags: Field
  type: Field
  createTime: Field
}

export interface Field {
  name: string
  type: string
  supportTypes: string[]
}

/**
 * 获取Notion文章数据库字段映射
 */
export const getArticleDatabaseFieldsAPI = () => {
  return http.get<ArticleFieldDTO>('/miniprogram/notion/getArticleDatabaseFields')
}

/**
 * 获取Notion消息数据库字段映射
 */
export const getMessageDatabaseFieldsAPI = () => {
  return http.get<MessageFieldDTO>('/miniprogram/notion/getMessageDatabaseFields')
}

/**
 * 保存Notion文章数据库字段映射
 */
export const saveArticleDatabaseFieldsAPI = (mappingData: ArticleFieldDTO) => {
  return http.post<boolean>('/miniprogram/notion/saveArticleDatabaseFields', mappingData)
}

/**
 * 保存Notion文章数据库字段映射
 */
export const saveMessageDatabaseFieldsAPI = (mappingData: MessageFieldDTO) => {
  return http.post<boolean>('/miniprogram/notion/saveMessageDatabaseFields', mappingData)
}

export interface NotionDbRes {
  databaseId: string // 属性名称
  properties: NotionDatabaseProperty[] // 属性类型
}

/**
 * 获取Notion文章数据库可用属性列表
 */
export interface NotionDatabaseProperty {
  name: string // 属性名称
  type: string // 属性类型
}

/**
 * 获取Notion文章数据库可用属性列表
 */
export const getArticleDatabasePropertiesAPI = () => {
  return http.get<NotionDbRes>('/miniprogram/notion/getArticleDatabaseProperties')
}

/**
 * 获取Notion文章数据库可用属性列表
 */
export const getMessageDatabasePropertiesAPI = () => {
  return http.get<NotionDbRes>('/miniprogram/notion/getMessageDatabaseProperties')
}
