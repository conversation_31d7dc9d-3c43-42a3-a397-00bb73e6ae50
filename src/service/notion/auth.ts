import { http } from '@/utils/http'

/**
 * 获取Notion授权码
 */
export const getAuthCode = () => {
  return http.get<string>('/notion/getAuthCode')
}

/**
 * 获取Notion授权码
 */
export const getAuthUrl = () => {
  return http.get<string>('/notion/getAuthUrl')
}

/**
 * 检查Notion授权状态
 */
export const checkAuth = () => {
  return http.get<boolean>('/miniprogram/checkNotionBind')
}

/**
 * 解除Notion授权
 */
export const removeAuth = () => {
  return http.get<boolean>('/miniprogram/removeAuth')
}
