import { http } from '@/utils/http'

export interface OrderRQ {
  goodsNo: string
  amount: number
  discountCode: string
}

export interface WxPayment {
  wxTradeId: string
  appId: number
  timeStamp: string
  nonceStr: string
  _package: string
  signType: string
  paySign: string
}

export const submitOrder = (body: OrderRQ) => {
  return http.post<WxPayment>('/miniprogram/submitOrder', body)
}

export interface Goods {
  goodsNo: string
  goodsName: string
  description: string
  price: number
  discount: number
}

export const goodsList = () => {
  return http.get<Goods[]>('/miniprogram/goodsList')
}

export const getDiscount = () => {
  return http.get<number>('/miniprogram/getDiscount')
}

export const checkPromotionCode = (promotionCode: string) => {
  return http.post<boolean>('/miniprogram/checkPromotionCode', { promotionCode })
}
