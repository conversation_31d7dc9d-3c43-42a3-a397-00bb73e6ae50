import { http } from '@/utils/http'

/**
 * 查看是否开启增强功能，感觉可以去掉这个接口。
 * 只要配置了增强图床就算开启，没有配置就没有开启。
 */
export const getEnhanceSwitch = () => {
  return http.get<boolean>('/miniprogram/getEnhanceSwitch')
}

/**
 * 设置增强功能开关
 * @param enhanceSwitch 开关状态
 * @returns 是否成功
 */
export const enhanceSwitch = (
  enableOrParams: boolean | { enable: boolean } | { value: boolean },
) => {
  // 处理不同类型的参数
  let enable: boolean

  if (typeof enableOrParams === 'boolean') {
    // 如果传入的是布尔值，直接使用
    enable = enableOrParams
  } else if ('enable' in enableOrParams) {
    // 如果传入的是 { enable: boolean } 对象
    enable = enableOrParams.enable
  } else if ('value' in enableOrParams) {
    // 如果传入的是 { value: boolean } 对象
    enable = enableOrParams.value
  } else {
    throw new Error('参数格式错误')
  }

  // 构造参数对象，确保发送给服务端的是 { "enable": true/false } 结构
  const params = { enable }
  return http.post<boolean>('/miniprogram/enhanceSwitchV2', params)
}

/**
 * 获取绑定口令
 * @returns 绑定口令
 */
export const getBindCpWxUserSecret = () => {
  return http.get<string>('/miniprogram/getBindCpWxUserSecret')
}

/**
 * 是否绑定了企微收藏助手
 * @returns 是否绑定
 */
export const getHasBindCpWxUser = () => {
  return http.get<boolean>('/miniprogram/getHasBindCpWxUser')
}
