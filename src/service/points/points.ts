import { http } from '@/utils/http'

export interface Points {
  promotionCode: string
  sum: number
  useSum: number
  availableSum: number
  shareCount: number
}

export interface Item {
  promotionCode: string
  objectId: number
  credit: number
  status: number
  gmtCreate: string
}

/**
 * 获取推广数据
 */
export const getPromotionData = () => {
  return http.get<Points>('/miniprogram/getPromotionData')
}

export const creditList = () => {
  return http.get<Item[]>('/miniprogram/creditList')
}
