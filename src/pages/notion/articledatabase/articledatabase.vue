<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '文章数据库',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="articledatabase-container">
    <!-- 顶部状态卡片 -->
    <wd-card class="mb-4 shadow rounded-lg mx-[16rpx]">
      <view class="px-[16rpx] flex justify-between items-center">
        <view>
          <wd-text size="16px" :text="mappingStatusText" :color="mappingStatusColor" bold />
        </view>
        <!-- 编辑锁 -->
        <view @click="toggleEditMode" class="p-[8rpx]">
          <view
            v-if="isEditMode"
            class="i-solar:lock-unlocked-bold w-[48rpx] h-[48rpx] c-#67c23a"
          ></view>
          <view v-else class="i-solar:lock-keyhole-bold w-[48rpx] h-[48rpx] c-#666666"></view>
        </view>
      </view>
    </wd-card>
    <view class="px-[16px] w-690rpx text-center mb-4 font-size-[12px] c-#696969">
      确保你要绑定的数据库名称是"
      <text class="text-[#337ea9]">文章数据库</text>
      ",并已添加授权
    </view>

    <!-- 字段映射表单 -->
    <wd-card class="mb-4 tr-shadow rounded-lg">
      <wd-form :model="formData" label-align="right">
        <view>
          <wd-text size="16px" text="字段映射配置" color="#3a3a3a" bold />
          <wd-gap height="16px"></wd-gap>

          <!-- 动态字段映射 -->
          <wd-cell-group border custom-class="rounded-lg overflow-hidden">
            <!-- 原文链接 -->
            <wd-cell
              title="原文链接"
              :value="formData.url.name === null ? '不设置' : formData.url.name || '请选择'"
              :disabled="!isEditMode"
              is-link
              @click="handleCellClick('url')"
            />

            <!-- 作者 -->
            <wd-cell
              title="作者"
              :value="formData.author.name === null ? '不设置' : formData.author.name || '请选择'"
              :disabled="!isEditMode"
              is-link
              @click="handleCellClick('author')"
            />

            <!-- 来源 -->
            <wd-cell
              title="来源"
              :value="formData.origin.name === null ? '不设置' : formData.origin.name || '请选择'"
              :disabled="!isEditMode"
              is-link
              @click="handleCellClick('origin')"
            />

            <!-- 标签 -->
            <wd-cell
              title="标签"
              :value="formData.tags.name === null ? '不设置' : formData.tags.name || '请选择'"
              :disabled="!isEditMode"
              is-link
              @click="handleCellClick('tags')"
            />

            <!-- 备注 -->
            <wd-cell
              title="备注"
              :value="formData.remark.name === null ? '不设置' : formData.remark.name || '请选择'"
              :disabled="!isEditMode"
              is-link
              @click="handleCellClick('remark')"
            />

            <!-- 发布时间 -->
            <wd-cell
              title="发布时间"
              :value="
                formData.publishTime.name === null
                  ? '不设置'
                  : formData.publishTime.name || '请选择'
              "
              :disabled="!isEditMode"
              is-link
              @click="handleCellClick('publishTime')"
            />

            <!-- 创建时间 -->
            <wd-cell
              title="创建时间"
              :value="
                formData.createTime.name === null ? '不设置' : formData.createTime.name || '请选择'
              "
              :disabled="!isEditMode"
              is-link
              @click="handleCellClick('createTime')"
            />
          </wd-cell-group>

          <view
            class="form-tips text-xs text-gray-500 mt-3 mb-6 px-2 flex flex-col items-start justify-start gap-1"
          >
            <wd-text text="* 字段留空表示不保存该字段信息"></wd-text>
            <wd-text text="* 原文链接和创建时间为必选字段"></wd-text>
          </view>
        </view>
      </wd-form>
    </wd-card>

    <!-- 底部按钮 -->
    <view class="p-[32rpx]" v-if="isEditMode">
      <wd-button type="primary" block @click="saveMapping">保存配置</wd-button>
    </view>

    <!-- 属性选择弹窗 -->
    <wd-action-sheet
      v-model="showActionSheet"
      :actions="propertyOptions"
      title="选择Notion属性"
      @select="handlePropertySelect"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useToast } from 'wot-design-uni'
import { useUserStore } from '@/store'
import {
  getArticleDatabaseFieldsAPI,
  getArticleDatabasePropertiesAPI,
  saveArticleDatabaseFieldsAPI,
  NotionDatabaseProperty,
  ArticleFieldDTO,
} from '@/service/notion/database'
import { extractErrorMessage } from '@/utils'

const toast = useToast()
const userStore = useUserStore()

// 编辑模式状态
const isEditMode = ref(false)

// 属性选择器状态
const showActionSheet = ref(false)
const propertyOptions = ref<{ name: string; value: string; subname?: string }[]>([])
const currentField = ref('')

// 处理单元格点击事件
const handleCellClick = (field: string) => {
  if (isEditMode.value) {
    // 如果已经在编辑模式，直接显示属性选择器
    showPropertyPicker(field)
  } else {
    // 如果在锁定状态，询问用户是否需要自定义
    uni.showModal({
      title: '自定义字段映射',
      content: `是否需要修改「${formData[field].field}」字段？需要先解锁编辑模式。`,
      confirmText: '确认',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户确认，触发编辑模式切换
          // 存储当前字段，以便解锁后使用
          currentField.value = field
          // 触发与点击锁图标相同的流程
          toggleEditMode(true) // 传入true表示解锁后需要显示属性选择器
        }
        // 用户取消，不做任何操作
      },
    })
  }
}

// 数据库属性列表
const databaseProperties = ref<NotionDatabaseProperty[]>([])

// 表单数据
const formData = reactive({
  title: { field: '标题', name: 'Name', type: 'title', supportTypes: ['title'] },
  url: { field: '原文链接', name: '', type: '', supportTypes: [] },
  author: { field: '作者', name: '', type: '', supportTypes: [] },
  origin: { field: '来源', name: '', type: '', supportTypes: [] },
  tags: { field: '标签', name: '', type: '', supportTypes: [] },
  remark: { field: '备注', name: '', type: '', supportTypes: [] },
  publishTime: { field: '发布时间', name: '', type: '', supportTypes: [] },
  createTime: { field: '创建时间', name: '', type: '', supportTypes: [] },
})

// 映射状态文本
const mappingStatusText = computed(() => {
  if (isEditMode.value) {
    return '编辑开放状态'
  } else {
    return '编辑锁定状态'
  }
})

// 映射状态颜色
const mappingStatusColor = computed(() => {
  if (isEditMode.value) {
    return '#67c23a'
  } else {
    return '#333333'
  }
})

// 初始化
onShow(async () => {
  // 检查授权状态
  if (!userStore.userInfo.notionAuth) {
    toast.error('请先授权Notion')
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/notion/authorize/authorize',
      })
    }, 1500)
    return
  }

  // 获取已保存的字段映射
  await loadDatabaseFields()
})

// 加载数据库字段映射
const loadDatabaseFields = async () => {
  try {
    toast.loading('加载中...')
    const { data, msg } = await getArticleDatabaseFieldsAPI()
    toast.close()

    if (data && Object.keys(data).length > 0) {
      // 将返回的数据填充到表单中
      const fields = ['url', 'author', 'origin', 'tags', 'remark', 'publishTime', 'createTime']

      fields.forEach((key) => {
        if (data[key]) {
          formData[key].name = data[key].name
          formData[key].type = data[key].type
          if (data[key].supportTypes) {
            formData[key].supportTypes = data[key].supportTypes
          }
        }
      })
    } else if (msg) {
      toast.error(msg)
    } else {
      toast.info('暂无配置数据')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    toast.close()
    console.error('加载数据库字段映射失败:', errorMessage)
    toast.error(`加载失败: ${errorMessage || '未知错误'}`)
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 保存表单数据的备份，用于取消编辑时恢复
let formDataBackup = {}

// 解锁编辑模式
const unlockEditMode = async (): Promise<boolean> => {
  try {
    toast.loading('加载数据库属性...')
    const { data, msg } = await getArticleDatabasePropertiesAPI()
    toast.close()
    if (data && data.properties && data.properties.length > 0) {
      databaseProperties.value = data.properties
      // 在进入编辑模式前备份当前表单数据
      formDataBackup = JSON.parse(JSON.stringify(formData))
      isEditMode.value = true
      toast.success('解锁成功')
      return true
    } else {
      toast.error(msg || '数据库属性为空')
      return false
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    toast.close()
    console.error('加载数据库属性失败:', errorMessage)
    toast.error(`加载失败: ${errorMessage || '未知错误'}`)
    return false
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 切换编辑模式
const toggleEditMode = (showPickerAfterUnlock: boolean = false) => {
  if (!isEditMode.value) {
    // 从未编辑状态切换到编辑状态，需要确认
    uni.showModal({
      title: '确认编辑',
      content: '编辑模式将查询您的文章数据库现有属性信息，是否继续？',
      confirmText: '确认',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          // 用户确认，解锁编辑模式
          const unlocked = await unlockEditMode()

          // 如果解锁成功且需要显示属性选择器
          if (unlocked && showPickerAfterUnlock && currentField.value) {
            showPropertyPicker(currentField.value)
          }
        }
        // 用户取消，不做任何操作
      },
    })
  } else {
    // 检查是否有未保存的修改
    const hasChanges = JSON.stringify(formData) !== JSON.stringify(formDataBackup)

    if (hasChanges) {
      uni.showModal({
        title: '确认退出编辑',
        content: '您有未保存的修改，确定要退出编辑模式吗？',
        confirmText: '确认',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户确认退出，恢复备份的数据
            Object.keys(formDataBackup).forEach((key) => {
              if (formData[key]) {
                formData[key] = JSON.parse(JSON.stringify(formDataBackup[key]))
              }
            })

            isEditMode.value = false
          }
          // 用户取消，保持编辑模式
        },
      })
    } else {
      // 没有修改，直接退出编辑模式
      isEditMode.value = false
    }
  }
}

// 显示属性选择器
const showPropertyPicker = (field: string) => {
  currentField.value = field

  // 获取字段支持的类型列表
  const supportTypes = formData[field].supportTypes || []

  // 如果字段没有设置supportTypes，使用默认值
  if (supportTypes.length === 0) {
    // 根据字段名设置默认支持的类型
    switch (field) {
      case 'url':
        formData[field].supportTypes = ['url', 'rich_text']
        break
      case 'author':
      case 'origin':
        formData[field].supportTypes = ['select', 'rich_text']
        break
      case 'tags':
        formData[field].supportTypes = ['multi_select']
        break
      case 'remark':
        formData[field].supportTypes = ['rich_text']
        break
      case 'publishTime':
        formData[field].supportTypes = ['date']
        break
      case 'createTime':
        formData[field].supportTypes = ['created_time', 'date']
        break
    }
  }

  // 检查数据库属性是否已加载
  if (databaseProperties.value.length === 0) {
    toast.error('数据库属性列表为空，请先解锁编辑模式')
    return
  }

  // 根据字段支持的类型过滤属性
  const filteredProperties = databaseProperties.value.filter((prop) => {
    // 检查属性类型是否在字段支持的类型列表中
    return formData[field].supportTypes.includes(prop.type)
  })
  console.log('过滤后的属性列表:', filteredProperties)

  // 转换为选择器选项格式
  propertyOptions.value = [
    // 添加“不设置”选项
    {
      name: '不设置',
      value: 'null',
      subname: '不使用该字段',
    },
    // 原有的属性选项
    ...filteredProperties.map((prop) => ({
      name: prop.name,
      value: prop.name,
      subname: getPropertyTypeLabel(prop.type),
    })),
  ]

  // 如果是必填字段，不显示“不设置”选项
  if (field === 'url' || field === 'createTime') {
    propertyOptions.value = propertyOptions.value.filter((option) => option.value !== 'null')
  }

  console.log('选择器选项:', propertyOptions.value)

  showActionSheet.value = true
}

// 获取属性类型标签
const getPropertyTypeLabel = (type: string) => {
  const typeMap = {
    text: '文本',
    rich_text: '富文本',
    select: '单选',
    multi_select: '多选',
    date: '日期',
    created_time: '创建时间',
    url: '网址',
    URL: '网址',
    title: '标题',
  }
  return typeMap[type] || type
}

// 处理属性选择
const handlePropertySelect = ({
  item,
}: {
  item: { name: string; value: string; subname?: string }
  index?: number
}) => {
  // 获取当前操作的字段
  const field = currentField.value

  // 如果选择了“不设置”选项
  if (item.value === 'null') {
    // 创建整个 formData 的副本
    const newFormData = { ...formData }

    // 创建字段的副本，设置为 null
    newFormData[field] = {
      ...newFormData[field],
      name: null,
      type: null,
    }

    // 使用 Object.assign 更新整个 formData 对象
    Object.keys(newFormData).forEach((key) => {
      formData[key] = { ...newFormData[key] }
    })

    // 强制刷新视图
    nextTick(() => {
      // 显示成功提示
      toast.success(`已设置 ${formData[field].field} 为不使用`)

      // 关闭弹框
      showActionSheet.value = false
    })
    return
  }

  const property = databaseProperties.value.find((p) => p.name === item.value)

  if (property && field) {
    // 创建整个 formData 的副本
    const newFormData = { ...formData }

    // 创建字段的副本
    newFormData[field] = {
      ...newFormData[field],
      name: property.name,
      type: property.type,
    }

    // 确保字段的 supportTypes 包含当前选择的属性类型
    if (!newFormData[field].supportTypes.includes(property.type)) {
      newFormData[field].supportTypes = [...newFormData[field].supportTypes, property.type]
    }

    // 使用 Object.assign 更新整个 formData 对象
    Object.keys(newFormData).forEach((key) => {
      formData[key] = { ...newFormData[key] }
    })

    // 强制刷新视图
    nextTick(() => {
      // 显示成功提示
      toast.success(`已选择 ${property.name} 作为 ${formData[field].field}`)

      // 关闭弹框
      showActionSheet.value = false
    })
  }
}

// 保存映射配置
const saveMapping = async () => {
  // 验证必填字段
  if (!formData.url.name) {
    toast.error('请选择原文链接对应的属性')
    return
  }

  if (!formData.createTime.name) {
    toast.error('请选择创建时间对应的属性')
    return
  }

  // 更新表单数据备份
  formDataBackup = JSON.parse(JSON.stringify(formData))

  // 检查是否有重复选择的属性
  const propertyMap = new Map()

  // 收集所有已设置的属性名称及其对应的字段
  Object.entries(formData).forEach(([fieldKey, fieldValue]) => {
    if (fieldValue.name) {
      if (!propertyMap.has(fieldValue.name)) {
        propertyMap.set(fieldValue.name, [fieldKey])
      } else {
        propertyMap.get(fieldValue.name).push(fieldKey)
      }
    }
  })

  // 检查是否有重复的属性
  const duplicates = Array.from(propertyMap.entries()).filter(([_, fields]) => fields.length > 1)

  if (duplicates.length > 0) {
    // 构建错误消息，显示哪些字段映射到了相同的属性
    const errorMessages = duplicates.map(([propName, fields]) => {
      const fieldNames = fields
        .map((field: string) => formData[field as keyof typeof formData].field)
        .join('、')
      return `属性「${propName}」被用于多个字段：${fieldNames}`
    })

    toast.error(`不同字段不能映射到同一个Notion属性\n${errorMessages.join('\n')}`)
    return
  }

  try {
    toast.loading('保存中...')

    // 获取数据库ID
    let databaseId = ''
    try {
      const { data: dbData } = await getArticleDatabasePropertiesAPI()
      if (dbData && dbData.databaseId) {
        databaseId = dbData.databaseId
      }
    } catch (e) {
      console.error('获取数据库ID失败:', e)
    }

    // 转换为API需要的格式
    // 创建一个符合 ArticleFieldDTO 接口的对象
    const mappingData: ArticleFieldDTO = {
      databaseId,
      url: {
        name: formData.url.name,
        type: formData.url.type,
        supportTypes: ['url', 'rich_text'], // 默认支持的类型
      },
      author: {
        name: formData.author.name,
        type: formData.author.type,
        supportTypes: ['select', 'rich_text'],
      },
      origin: {
        name: formData.origin.name,
        type: formData.origin.type,
        supportTypes: ['select', 'rich_text'],
      },
      tags: {
        name: formData.tags.name,
        type: formData.tags.type,
        supportTypes: ['multi_select'],
      },
      remark: {
        name: formData.remark.name,
        type: formData.remark.type,
        supportTypes: ['rich_text'],
      },
      publishTime: {
        name: formData.publishTime.name,
        type: formData.publishTime.type,
        supportTypes: ['date'],
      },
      createTime: {
        name: formData.createTime.name,
        type: formData.createTime.type,
        supportTypes: ['created_time', 'date'],
      },
    }

    // 处理没有设置的字段
    // 如果字段的 name 是 null，保持为 null，表示用户选择了“不设置”
    // 如果字段的 name 是空字符串，设置为空值
    Object.keys(mappingData).forEach((key) => {
      if (key !== 'databaseId') {
        if (mappingData[key].name === null) {
          // 保持为 null，表示用户选择了“不设置”
          mappingData[key] = { name: null, type: null, supportTypes: [] }
        } else if (!mappingData[key].name) {
          // 如果是空字符串，设置为空值
          mappingData[key] = { name: '', type: '', supportTypes: [] }
        }
      }
    })

    const { data } = await saveArticleDatabaseFieldsAPI(mappingData)

    if (data) {
      toast.success('保存成功')
      isEditMode.value = false
    } else {
      toast.error('保存失败')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    console.error('保存映射配置失败:', errorMessage)
    toast.error(`保存失败: ${errorMessage || '未知错误'}`)
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}
</script>

<style lang="scss" scoped>
.articledatabase-container {
  min-height: 100vh;
  padding-bottom: 32rpx;
  margin-top: 16px;
  background-color: #f8f8f7;
}

.form-item {
  padding: 10rpx 0;
}
</style>
