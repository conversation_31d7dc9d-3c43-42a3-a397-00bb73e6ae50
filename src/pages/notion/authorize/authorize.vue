<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'Notion授权',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="authorize-container">
    <!-- 未授权状态 -->
    <view v-if="!isAuthorized" class="flex flex-col items-center justify-center p-4">
      <!-- Notion Logo -->
      <image class="w-200rpx h-200rpx mb-8 mt-8" src="/static/images/operate/notionHelper.png" />

      <!-- 标题 -->
      <wd-text text="授权 Notion 账户" size="24px" bold custom-class="mb-4 !c-#333" />

      <!-- 说明文字 -->
      <wd-text
        text="授权完成后便可以将内容同步到 Notion 中"
        size="16px"
        custom-class="mb-6 !c-#666 text-center"
      />

      <!-- 授权步骤 -->
      <view class="w-90% bg-white rounded-16px p-4 mb-6 tr-shadow">
        <view class="flex flex-row items-center mb-4">
          <view
            class="w-40rpx h-40rpx bg-#337ea9 rounded-full flex items-center justify-center c-white font-bold"
          >
            1
          </view>
          <wd-text text="点击下方 ‘复制授权链接’ 按钮" size="16px" custom-class="ml-3 !c-#333" />
        </view>

        <view class="flex flex-row items-center mb-4">
          <view
            class="w-40rpx h-40rpx bg-#337ea9 rounded-full flex items-center justify-center c-white font-bold"
          >
            2
          </view>
          <wd-text text="在浏览器中打开授权链接" size="16px" custom-class="ml-3 !c-#333" />
        </view>

        <view class="flex flex-row items-center mb-4">
          <view
            class="w-40rpx h-40rpx bg-#337ea9 rounded-full flex items-center justify-center c-white font-bold"
          >
            3
          </view>
          <wd-text text="按照使用手册说明进行授权操作" size="16px" custom-class="ml-3 !c-#333" />
        </view>
        <view class="flex flex-row items-center">
          <view
            class="w-40rpx h-40rpx bg-#337ea9 rounded-full flex items-center justify-center c-white font-bold"
          >
            4
          </view>
          <wd-text
            text="确认授权后返回此页面 ‘验证授权状态’ "
            size="16px"
            custom-class="ml-3 !c-#333"
          />
        </view>
      </view>

      <!-- 授权按钮 -->
      <wd-button type="primary" custom-class="w-90% h-100rpx tr-shadow" @click="handleAuthorize">
        复制授权链接
      </wd-button>
      <wd-gap height="16px" />
      <!-- 刷新授权状态 -->
      <wd-button type="primary" custom-class="w-90% h-100rpx tr-shadow" @click="handleVerifyAuth">
        验证授权状态
      </wd-button>
    </view>

    <!-- 已授权状态 -->
    <view v-else class="flex flex-col items-center justify-center p-4">
      <!-- 授权信息 -->
      <view
        class="w-90% bg-white rounded-16px p-4 tr-shadow flex flex-col items-start justify-start"
      >
        <wd-text text="授权状态" size="16px" bold custom-class="mb-2 !c-#333" />
        <view class="flex flex-row items-center mb-4">
          <view class="w-16rpx h-16rpx rounded-full bg-#67c23a mr-2"></view>
          <wd-text text="已成功连接到您的 Notion 账户" size="14px" custom-class="!c-#666" />
        </view>
        <wd-gap height="1px" bg-color="#f5f5f5" custom-class="w-full" />

        <wd-text text="授权成功还是无法保存？" size="16px" bold custom-class="mt-4 mb-2 !c-#333" />
        <wd-text
          text="检查一：点击下方 ‘验证授权状态’ 核实授权状态"
          size="14px"
          custom-class="mb-2 !c-#666"
        />
        <wd-text
          text="检查二：是否修改过 Notion 数据库字段"
          size="14px"
          custom-class="mb-2 !c-#666"
        />
        <wd-text
          text="方案A：解除授权，重新走一遍授权流程"
          size="14px"
          custom-class="mb-2 !c-#666"
        />
        <wd-text
          text="方案B：返回上一页进入对应数据库页面重新选择数据库字段"
          size="14px"
          custom-class="mb-2 !c-#666"
        />

        <wd-gap height="1px" bg-color="#f5f5f5" custom-class="w-full" />

        <wd-text text="如何解除授权？" size="16px" bold custom-class="mt-4 mb-2 !c-#333" />
        <wd-text text="1. 打开 Notion 应用" size="14px" custom-class="mb-2 !c-#666" />
        <wd-text
          text="2. 找到已授权页面，点击页面右上角的设置按钮"
          size="14px"
          custom-class="mb-2 !c-#666"
        />
        <wd-text text="3. 在集成中找到并移除对应集成" size="14px" custom-class="mb-2 !c-#666" />
        <wd-text
          text="4. 回到小程序此页面，点击下方的解除授权按钮"
          size="14px"
          custom-class="mb-2 !c-#666"
        />
      </view>
      <!-- 操作按钮 -->
      <view class="flex flex-row items-center justify-between w-full mt-4">
        <wd-button
          type="error"
          plain
          custom-class="mx-4 h-90rpx  w-90% tr-shadow"
          @click="removeReauthorize"
        >
          确认解除授权
        </wd-button>
        <wd-button
          type="primary"
          plain
          custom-class="mx-4 h-90rpx w-90% tr-shadow"
          @click="handleVerifyAuth"
        >
          验证授权状态
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useToast } from 'wot-design-uni'
import { useUserStore } from '@/store'
import { checkAuth, getAuthUrl, removeAuth } from '@/service/notion/auth'
import { extractErrorMessage } from '@/utils'

const toast = useToast()
const userStore = useUserStore()

// 授权状态
const isAuthorized = ref(false)

// 初始化时检查授权状态
onShow(() => {
  checkAuthStatus()
})

// 检查授权状态
const checkAuthStatus = () => {
  // 从用户信息中获取授权状态
  const { notionAuth } = userStore.userInfo
  isAuthorized.value = !!notionAuth
}

// 处理授权
const handleAuthorize = async () => {
  toast.loading('正在获取授权链接...')

  try {
    // 调用获取授权链接API
    const { data: authUrl } = await getAuthUrl()

    if (authUrl) {
      // 复制授权链接到剪贴板
      wx.hideToast()
      uni.setClipboardData({
        data: authUrl,
        success: () => {
          wx.hideToast()
          toast.success('授权链接已复制到剪贴板')
        },
        fail: () => {
          wx.hideToast()
          toast.error('复制授权链接失败')
        },
      })
    } else {
      wx.hideToast() // Added wx.hideToast() for consistency
      toast.error('获取授权链接失败')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    console.error('获取授权链接失败:', errorMessage)
    toast.error(errorMessage || '获取授权链接失败，请重试')
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 验证授权
const handleVerifyAuth = async () => {
  toast.loading('正在验证授权状态...')

  try {
    // 调用实际的授权检查API
    const { data } = await checkAuth()

    // 更新用户store中的授权状态
    userStore.userInfo.notionAuth = data
    isAuthorized.value = data

    if (data) {
      toast.success('授权有效')
    } else {
      toast.error('授权已失效，请重新授权')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    console.error('验证授权状态失败:', errorMessage)
    toast.error(errorMessage || '验证授权状态失败，请重试')
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 解除授权
const removeReauthorize = async () => {
  toast.loading('正在解除授权...')

  try {
    // 调用解除授权API
    const { data } = await removeAuth()

    if (data) {
      // 如果解除授权成功，更新用户store和页面状态
      userStore.userInfo.notionAuth = false
      isAuthorized.value = false
      toast.success('已解除授权，请重新授权')
    } else {
      toast.error('解除授权失败，请重试')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    console.error('解除授权失败:', errorMessage)
    toast.error(errorMessage || '解除授权失败，请重试')
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}
</script>

<style lang="scss" scoped>
.authorize-container {
  min-height: 100vh;
  background-color: #f8f8f7;
}
</style>
