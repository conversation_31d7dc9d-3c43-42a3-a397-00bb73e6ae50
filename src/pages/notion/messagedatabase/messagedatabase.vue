<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '消息数据库',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="messagedatabase-container">
    <!-- 顶部状态卡片 -->
    <wd-card class="mb-4 shadow rounded-lg mx-[16rpx] mt-[16rpx]">
      <view class="px-[16rpx] flex justify-between items-center">
        <view>
          <wd-text size="16px" :text="mappingStatusText" :color="mappingStatusColor" bold />
        </view>
        <!-- 编辑锁 -->
        <view @click="toggleEditMode" class="p-[8rpx]">
          <view
            v-if="isEditMode"
            class="i-solar:lock-unlocked-bold w-[48rpx] h-[48rpx] c-#67c23a"
          ></view>
          <view v-else class="i-solar:lock-keyhole-bold w-[48rpx] h-[48rpx] c-#666666"></view>
        </view>
      </view>
    </wd-card>
    <view class="px-[16px] w-690rpx text-center mb-4 font-size-[12px] c-#696969">
      确保你要绑定的数据库名称是"
      <text class="text-[#337ea9]">消息数据库</text>
      ",并已添加授权
    </view>

    <!-- 字段映射表单 -->
    <wd-card class="mb-4 tr-shadow rounded-lg">
      <wd-form :model="formData" label-align="right">
        <view>
          <wd-text size="16px" text="字段映射配置" color="#3a3a3a" bold />
          <wd-gap height="16px"></wd-gap>

          <!-- 动态字段映射 -->
          <wd-cell-group border custom-class="rounded-lg overflow-hidden">
            <!-- 类型 -->
            <wd-cell
              title="类型"
              :value="formData.type.name || '请选择'"
              :disabled="!isEditMode"
              is-link
              @click="handleCellClick('type')"
            />

            <!-- 标签 -->
            <wd-cell
              title="标签"
              :value="formData.tags.name || '请选择'"
              :disabled="!isEditMode"
              is-link
              @click="handleCellClick('tags')"
            />

            <!-- 创建时间 -->
            <wd-cell
              title="创建时间"
              :value="formData.createTime.name || '请选择'"
              :disabled="!isEditMode"
              is-link
              @click="handleCellClick('createTime')"
            />
          </wd-cell-group>

          <view
            class="form-tips text-xs text-gray-500 mt-3 mb-6 px-2 flex flex-col items-start justify-start gap-1"
          >
            <wd-text text="* 字段留空表示不保存该字段信息"></wd-text>
            <wd-text text="* 创建时间为必选字段"></wd-text>
          </view>
        </view>
      </wd-form>
    </wd-card>

    <!-- 底部按钮 -->
    <view class="p-[32rpx]" v-if="isEditMode">
      <wd-button type="primary" block @click="saveMapping">保存配置</wd-button>
    </view>

    <!-- 属性选择弹窗 -->
    <wd-action-sheet
      v-model="showActionSheet"
      :actions="propertyOptions"
      title="选择Notion属性"
      @select="handlePropertySelect"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useToast } from 'wot-design-uni'
import { useUserStore } from '@/store'
import {
  getMessageDatabaseFieldsAPI,
  getMessageDatabasePropertiesAPI,
  saveMessageDatabaseFieldsAPI,
  NotionDatabaseProperty,
} from '@/service/notion/database'
import type { MessageFieldDTO } from '@/service/notion/database'
import { extractErrorMessage } from '@/utils'

const toast = useToast()
const userStore = useUserStore()

// 编辑模式状态
const isEditMode = ref(false)
// 数据库ID，优先从字段映射接口获取，编辑时可被覆盖
const databaseId = ref('')

// 属性选择器状态
const showActionSheet = ref(false)
const propertyOptions = ref<{ name: string; value: string; subname?: string }[]>([])
const currentField = ref('')

// 处理单元格点击事件
const handleCellClick = (field: string) => {
  if (isEditMode.value) {
    // 如果已经在编辑模式，直接显示属性选择器
    showPropertyPicker(field)
  } else {
    // 如果在锁定状态，询问用户是否需要自定义
    uni.showModal({
      title: '自定义字段映射',
      content: `是否需要修改「${formData[field].field}」字段？需要先解锁编辑模式。`,
      confirmText: '确认',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户确认，触发编辑模式切换
          // 存储当前字段，以便解锁后使用
          currentField.value = field
          // 触发与点击锁图标相同的流程
          toggleEditMode(true) // 传入true表示解锁后需要显示属性选择器
        }
        // 用户取消，不做任何操作
      },
    })
  }
}

// 数据库属性列表
const databaseProperties = ref<NotionDatabaseProperty[]>([])

// 表单数据
const formData = reactive({
  type: { field: '类型', name: '', type: '', supportTypes: [] },
  tags: { field: '标签', name: '', type: '', supportTypes: [] },
  createTime: { field: '创建时间', name: '', type: '', supportTypes: [] },
})

// 映射状态文本
const mappingStatusText = computed(() => {
  if (isEditMode.value) {
    return '编辑开放状态'
  } else {
    return '编辑锁定状态'
  }
})

// 映射状态颜色
const mappingStatusColor = computed(() => {
  if (isEditMode.value) {
    return '#67c23a'
  } else {
    return '#333333'
  }
})

// 初始化
onShow(async () => {
  // 检查授权状态
  if (!userStore.userInfo.notionAuth) {
    toast.error('请先授权Notion')
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/notion/authorize/authorize',
      })
    }, 1500)
    return
  }

  // 获取已保存的字段映射
  await loadDatabaseFields()
})

// 加载数据库字段映射
const loadDatabaseFields = async () => {
  try {
    toast.loading('加载中...')
    const { data, msg } = await getMessageDatabaseFieldsAPI()

    if (data && Object.keys(data).length > 0) {
      // 赋值 databaseId
      if (data.databaseId) {
        databaseId.value = data.databaseId
      }
      // 将返回的数据填充到表单中
      Object.entries(data).forEach(([key, value]) => {
        // 根据字段名称匹配表单字段
        switch (key) {
          case 'type':
            formData.type.name = value.name
            formData.type.type = value.type
            if (value.supportTypes) {
              formData.type.supportTypes = value.supportTypes
            }
            break
          case 'tags':
            formData.tags.name = value.name
            formData.tags.type = value.type
            if (value.supportTypes) {
              formData.tags.supportTypes = value.supportTypes
            }
            break
          case 'createTime':
            formData.createTime.name = value.name
            formData.createTime.type = value.type
            if (value.supportTypes) {
              formData.createTime.supportTypes = value.supportTypes
            }
            break
        }
      })
      toast.close()
    } else if (msg) {
      toast.error(msg)
    } else {
      toast.info('暂无配置数据')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    console.error('加载数据库字段映射失败:', errorMessage)
    toast.error(`加载失败: ${errorMessage || '未知错误'}`)
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 保存表单数据的备份，用于取消编辑时恢复
let formDataBackup = {}

// 解锁编辑模式
const unlockEditMode = async (): Promise<boolean> => {
  try {
    toast.loading('加载数据库属性...')
    const { data, msg } = await getMessageDatabasePropertiesAPI()
    // data: NotionDbRes | undefined
    // data?.properties: NotionDatabaseProperty[]
    if (data && Array.isArray(data.properties) && data.properties.length > 0) {
      databaseProperties.value = data.properties
      // 用最新 databaseId 覆盖
      if (data.databaseId) {
        databaseId.value = data.databaseId
      }
      // 在进入编辑模式前备份当前表单数据
      formDataBackup = JSON.parse(JSON.stringify(formData))
      isEditMode.value = true
      toast.success('解锁成功')
      return true
    } else {
      toast.error(msg || '未获取到数据库属性')
      return false
    }
  } catch (error: any) {
    const errorMessage = extractErrorMessage(error)
    console.error('加载数据库属性失败:', errorMessage)
    toast.error(`加载失败: ${errorMessage || '未知错误'}`)
    return false
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 切换编辑模式
const toggleEditMode = (showPickerAfterUnlock: boolean = false) => {
  if (!isEditMode.value) {
    // 从未编辑状态切换到编辑状态，需要确认
    uni.showModal({
      title: '确认编辑',
      content: '编辑模式将查询您的消息数据库现有属性信息，是否继续？',
      confirmText: '确认',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          // 用户确认，解锁编辑模式
          const unlocked = await unlockEditMode()

          // 如果解锁成功且需要显示属性选择器
          if (unlocked && showPickerAfterUnlock && currentField.value) {
            showPropertyPicker(currentField.value)
          }
        }
        // 用户取消，不做任何操作
      },
    })
  } else {
    // 检查是否有未保存的修改
    const hasChanges = JSON.stringify(formData) !== JSON.stringify(formDataBackup)

    if (hasChanges) {
      uni.showModal({
        title: '确认退出编辑',
        content: '您有未保存的修改，确定要退出编辑模式吗？',
        confirmText: '确认',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户确认退出，恢复备份的数据
            Object.keys(formDataBackup).forEach((key) => {
              if (formData[key]) {
                formData[key] = JSON.parse(JSON.stringify(formDataBackup[key]))
              }
            })

            isEditMode.value = false
          }
          // 用户取消，保持编辑模式
        },
      })
    } else {
      // 没有修改，直接退出编辑模式
      isEditMode.value = false
    }
  }
}

// 显示属性选择器
const showPropertyPicker = (field: string) => {
  currentField.value = field

  // 获取字段支持的类型列表
  const supportTypes = formData[field].supportTypes || []

  // 如果字段没有设置supportTypes，使用默认值
  if (supportTypes.length === 0) {
    // 根据字段名设置默认支持的类型
    switch (field) {
      case 'type':
        formData[field].supportTypes = ['rich_text', 'select']
        break
      case 'tags':
        formData[field].supportTypes = ['multi_select']
        break
      case 'createTime':
        formData[field].supportTypes = ['created_time']
        break
    }
  }

  // 检查数据库属性是否已加载
  if (databaseProperties.value.length === 0) {
    toast.error('数据库属性列表为空，请先解锁编辑模式')
    return
  }

  // 根据字段支持的类型过滤属性
  const filteredProperties = databaseProperties.value.filter((prop) => {
    // 检查属性类型是否在字段支持的类型列表中
    return formData[field].supportTypes.includes(prop.type)
  })
  console.log('过滤后的属性列表:', filteredProperties)

  // 转换为选择器选项格式
  propertyOptions.value = filteredProperties.map((prop) => ({
    name: prop.name,
    value: prop.name,
    subname: getPropertyTypeLabel(prop.type),
  }))
  console.log('选择器选项:', propertyOptions.value)

  showActionSheet.value = true
}

// 获取属性类型标签
const getPropertyTypeLabel = (type: string) => {
  const typeMap = {
    text: '文本',
    rich_text: '富文本',
    select: '单选',
    multi_select: '多选',
    date: '日期',
    created_time: '创建时间',
    url: '网址',
    URL: '网址',
    title: '标题',
  }
  return typeMap[type] || type
}

// 处理属性选择
const handlePropertySelect = ({
  item,
}: {
  item: { name: string; value: string; subname?: string }
  index?: number
}) => {
  // 获取当前操作的字段
  const field = currentField.value

  const property = databaseProperties.value.find((p) => p.name === item.value)

  if (property && field) {
    // 创建整个 formData 的副本
    const newFormData = { ...formData }

    // 创建字段的副本
    newFormData[field] = {
      ...newFormData[field],
      name: property.name,
      type: property.type,
    }

    // 确保字段的 supportTypes 包含当前选择的属性类型
    if (!newFormData[field].supportTypes.includes(property.type)) {
      newFormData[field].supportTypes = [...newFormData[field].supportTypes, property.type]
    }

    // 使用 Object.assign 更新整个 formData 对象
    Object.keys(newFormData).forEach((key) => {
      formData[key] = { ...newFormData[key] }
    })

    // 强制刷新视图
    nextTick(() => {
      // 显示成功提示
      toast.success(`已选择 ${property.name} 作为 ${formData[field].field}`)

      // 关闭弹框
      showActionSheet.value = false
    })
  }
}

// 保存映射配置
const saveMapping = async () => {
  // 验证必填字段
  if (!formData.createTime.name) {
    toast.error('请选择创建时间对应的属性')
    return
  }

  // 更新表单数据备份
  formDataBackup = JSON.parse(JSON.stringify(formData))

  // 检查是否有重复选择的属性
  const propertyMap = new Map()

  // 收集所有已设置的属性名称及其对应的字段
  Object.entries(formData).forEach(([fieldKey, fieldValue]) => {
    if (fieldValue.name) {
      if (!propertyMap.has(fieldValue.name)) {
        propertyMap.set(fieldValue.name, [fieldKey])
      } else {
        propertyMap.get(fieldValue.name).push(fieldKey)
      }
    }
  })

  // 检查是否有重复的属性
  const duplicates = Array.from(propertyMap.entries()).filter(([_, fields]) => fields.length > 1)

  if (duplicates.length > 0) {
    // 构建错误消息，显示哪些字段映射到了相同的属性
    const errorMessages = duplicates.map(([propName, fields]) => {
      const fieldNames = fields
        .map((field: string) => formData[field as keyof typeof formData].field)
        .join('、')
      return `属性「${propName}」被用于多个字段：${fieldNames}`
    })

    toast.error(`不同字段不能映射到同一个Notion属性\n${errorMessages.join('\n')}`)
    return
  }

  try {
    toast.loading('保存中...')

    // 获取数据库ID
    let dbId = databaseId.value
    if (!dbId) {
      try {
        const { data: dbData } = await getMessageDatabasePropertiesAPI()
        if (dbData && dbData.databaseId) {
          dbId = dbData.databaseId
        }
      } catch (e) {
        console.error('获取数据库ID失败:', e)
      }
    }

    // 转换为API需要的格式
    const mappingData: MessageFieldDTO = {
      databaseId: dbId,
      type: {
        name: formData.type.name,
        type: formData.type.type,
        supportTypes: formData.type.supportTypes,
      },
      tags: {
        name: formData.tags.name,
        type: formData.tags.type,
        supportTypes: formData.tags.supportTypes,
      },
      createTime: {
        name: formData.createTime.name,
        type: formData.createTime.type,
        supportTypes: formData.createTime.supportTypes,
      },
    }

    // 过滤掉没有设置的字段 (注意：不能直接删除 MessageFieldDTO 的必需属性)
    // 而是将未设置的字段设置为空值
    Object.keys(mappingData).forEach((key) => {
      if (key !== 'databaseId' && !mappingData[key].name) {
        mappingData[key] = { name: '', type: '', supportTypes: [] }
      }
    })

    const { data } = await saveMessageDatabaseFieldsAPI(mappingData)

    if (data) {
      toast.success('保存成功')
      isEditMode.value = false
    } else {
      toast.error('保存失败')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    console.error('保存映射配置失败:', errorMessage)
    toast.error(`保存失败: ${errorMessage || '未知错误'}`)
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}
</script>

<style lang="scss" scoped>
.messagedatabase-container {
  min-height: 100vh;
  padding-top: 16px;
  padding-bottom: 32rpx;
  background-color: #f8f8f7;
}

.form-item {
  padding: 10rpx 0;
}
</style>
