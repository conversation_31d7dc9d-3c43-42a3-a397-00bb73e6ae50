<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '标签同步和备份',
  },
}
</route>

<template>
  <view class="tagsync-container">
    <!-- 页面标题和说明 -->
    <view class="header-section">
      <view class="title">标签管理</view>
      <view class="description">同步 Notion 标签到本地，或备份本地标签到服务器</view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="actions-section">
      <!-- 同步按钮 -->
      <view class="action-card">
        <view class="action-header">
          <view class="i-solar:download-minimalistic-linear w-20px h-20px mr-10px"></view>
          <view class="action-info">
            <view class="action-title">从 Notion 同步标签</view>
            <view class="action-desc">
              获取 Notion 文章数据库中的标签（最多100个）
              <br />
              与本地标签合并，本地标签保持优先
            </view>
          </view>
        </view>
        <button
          class="action-button sync-button"
          :disabled="tagStore.syncLoading"
          @click="handleSyncFromNotion"
        >
          <text v-if="tagStore.syncLoading">同步中...</text>
          <text v-else>开始同步</text>
        </button>
      </view>

      <!-- 备份按钮 -->
      <view class="action-card">
        <view class="action-header">
          <view class="i-solar:upload-minimalistic-linear w-20px h-20px mr-10px"></view>
          <view class="action-info">
            <view class="action-title">备份标签到服务器</view>
            <view class="action-desc">
              将当前本地标签备份到服务器
              <br />
              确保数据安全，防止丢失
            </view>
          </view>
        </view>
        <button
          class="action-button backup-button"
          :disabled="tagStore.backupLoading || localTagsCount === 0"
          @click="handleBackupToServer"
        >
          <text v-if="tagStore.backupLoading">备份中...</text>
          <text v-else-if="localTagsCount === 0">暂无标签</text>
          <text v-else>开始备份</text>
        </button>
      </view>

      <!-- 删除备份按钮 -->
      <view class="action-card">
        <view class="action-header">
          <view class="i-solar:trash-bin-trash-linear w-20px h-20px mr-10px"></view>
          <view class="action-info">
            <view class="action-title">删除服务器备份</view>
            <view class="action-desc">
              删除服务器上的标签备份数据
              <br />
              此操作不会影响本地标签
            </view>
          </view>
        </view>
        <button
          class="action-button delete-button"
          :disabled="tagStore.deleteLoading"
          @click="handleDeleteServerBackup"
        >
          <text v-if="tagStore.deleteLoading">删除中...</text>
          <text v-else>删除备份</text>
        </button>
      </view>
    </view>

    <!-- 本地标签预览 -->
    <view class="preview-section" v-if="localTags.length > 0">
      <view class="preview-title">本地标签预览</view>
      <view class="tags-container">
        <view class="tag-item" v-for="(tag, index) in localTags.slice(0, 20)" :key="index">
          {{ tag }}
        </view>
        <view class="tag-item more-tag" v-if="localTags.length > 20">
          +{{ localTags.length - 20 }} 更多
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'wot-design-uni'
import { useTagStore } from '@/store/tag'

// 使用标签存储
const tagStore = useTagStore()

// 初始化 toast
const toast = useToast()

// 计算属性
const localTags = computed(() => tagStore.getAllTags())
const localTagsCount = computed(() => localTags.value.length)

// 处理从 Notion 同步标签
const handleSyncFromNotion = async () => {
  console.log('用户点击从 Notion 同步标签')

  const result = await tagStore.syncTagsFromNotion()

  if (result.success) {
    toast.success(`同步成功，合并了 ${result.syncedCount} 个标签`)
  } else {
    toast.error('同步失败，请稍后重试')
  }
}

// 处理备份到服务器
const handleBackupToServer = async () => {
  console.log('用户点击备份标签到服务器')

  const result = await tagStore.backupTagsToServer()

  if (result.success) {
    toast.success(`备份成功，已备份 ${result.backupCount} 个标签`)
  } else {
    toast.error('备份失败，请稍后重试')
  }
}

// 处理删除服务器备份
const handleDeleteServerBackup = async () => {
  console.log('用户点击删除服务器备份')

  const result = await tagStore.deleteTagsFromServer()

  if (result.success) {
    toast.success('服务器备份删除成功')
  } else {
    toast.error('删除备份失败，请稍后重试')
  }
}

// 页面加载时初始化
onMounted(() => {
  console.log('标签同步页面加载完成')
  tagStore.initTags()
})
</script>

<style lang="scss" scoped>
.tagsync-container {
  min-height: 100vh;
  padding: 20rpx;
  background-color: #f8f9fa;
}

.header-section {
  margin-bottom: 40rpx;
  text-align: center;

  .title {
    margin-bottom: 16rpx;
    font-size: 48rpx;
    font-weight: bold;
    color: #1a1a1a;
  }

  .description {
    font-size: 28rpx;
    line-height: 1.5;
    color: #666;
  }
}

.stats-section {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;

  .stat-card {
    min-width: 200rpx;
    padding: 40rpx;
    color: white;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16rpx;

    .stat-number {
      margin-bottom: 8rpx;
      font-size: 56rpx;
      font-weight: bold;
    }

    .stat-label {
      font-size: 28rpx;
      opacity: 0.9;
    }
  }
}

.actions-section {
  margin-bottom: 40rpx;

  .action-card {
    padding: 32rpx;
    margin-bottom: 24rpx;
    background: white;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .action-header {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24rpx;

      .action-icon {
        margin-top: 8rpx;
        margin-right: 24rpx;
        font-size: 48rpx;
      }

      .action-info {
        flex: 1;

        .action-title {
          margin-bottom: 8rpx;
          font-size: 32rpx;
          font-weight: bold;
          color: #1a1a1a;
        }

        .action-desc {
          font-size: 26rpx;
          line-height: 1.6;
          color: #666;
        }
      }
    }

    .action-button {
      width: 100%;
      height: 88rpx;
      font-size: 32rpx;
      font-weight: bold;
      border: none;
      border-radius: 12rpx;

      &.sync-button {
        color: white;
        background-color: #337ea9;

        &:disabled {
          color: #999;
          background: #e0e0e0;
        }
      }

      &.backup-button {
        color: white;
        background-color: #337ea9;

        &:disabled {
          color: #999;
          background: #e0e0e0;
        }
      }

      &.delete-button {
        color: white;
        background-color: #d44c47;

        &:disabled {
          color: #999;
          background: #e0e0e0;
        }
      }
    }
  }
}

.preview-section {
  padding: 32rpx;
  margin-bottom: 40rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .preview-title {
    margin-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #1a1a1a;
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .tag-item {
      padding: 12rpx 24rpx;
      font-size: 26rpx;
      color: #333;
      background: #f0f0f0;
      border-radius: 20rpx;

      &.more-tag {
        color: #1890ff;
        background: #e8f4fd;
      }
    }
  }
}
</style>
