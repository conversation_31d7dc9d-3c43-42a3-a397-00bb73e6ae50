<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '编辑备注',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="remark-editor-container">
    <!-- 编辑区域 -->
    <view class="editor-section">
      <!-- 按钮区域 -->
      <view class="button-area">
        <view class="w-auto mr-4px">
          <wd-button
            icon="list"
            type="info"
            custom-class="!rounded-8rpx !h-66rpx"
            @click="getClipboardContent"
          >
            读取剪贴板
          </wd-button>
        </view>
        <view class="w-auto">
          <wd-button
            icon="clear"
            type="info"
            custom-class="!rounded-8rpx !h-66rpx"
            @click="clearContent"
          >
            清空内容
          </wd-button>
        </view>
      </view>
      <view class="editor-area">
        <wd-textarea
          v-model="remarkContent"
          placeholder="请输入备注内容..."
          :maxlength="500"
          :rows="10"
          custom-class="remark-input"
          auto-height
          no-border
        />
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-btn-container">
      <wd-button type="primary" block custom-class="confirm-btn" @click="confirmRemark">
        确认
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useToast } from 'wot-design-uni'

// 初始化toast
const toast = useToast()

// 备注内容
const remarkContent = ref('')

// 事件通道
let eventChannel: any = null

// 页面加载时获取传递的备注数据
onShow(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-expect-error 获取事件通道
  eventChannel = currentPage.getOpenerEventChannel()

  // 监听初始化事件
  eventChannel.on('initRemark', (data: { remark: string }) => {
    if (data && data.remark !== undefined) {
      remarkContent.value = data.remark
    }
  })
})

// 读取剪贴板内容
const getClipboardContent = () => {
  uni.getClipboardData({
    success: (res) => {
      if (res.data) {
        remarkContent.value = res.data
      } else {
        toast.warning('剪贴板内容为空')
      }
    },
    fail: () => {
      toast.error('读取剪贴板失败')
    },
  })
}

// 清空内容
const clearContent = () => {
  if (remarkContent.value) {
    remarkContent.value = ''
    toast.success('内容已清空')
  }
}

// 确认备注内容
const confirmRemark = () => {
  // 通过事件通道将备注数据传回上一页
  if (eventChannel) {
    eventChannel.emit('updateRemark', { remark: remarkContent.value })
  }

  // 返回上一页
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.remark-editor-container {
  min-height: 100vh;
  padding: 32rpx;
  background-color: #f8f8f8;
}

.editor-section {
  padding: 24rpx;
  margin-bottom: 40rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.editor-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.editor-area {
  margin-bottom: 20rpx;
}

.button-area {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
}

:deep(.remark-input) {
  width: auto;
  min-height: 400rpx;
  padding: 16rpx;
  background-color: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.confirm-btn {
  height: 88rpx;
  border-radius: 12rpx;
}
</style>
