<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '标签管理',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="tag-manager-container">
    <!-- 当前标签 -->
    <view class="section-container">
      <view class="section-title">当前标签</view>
      <view class="tags-container">
        <template v-if="currentTags.length === 0">
          <view class="empty-tip">暂无标签，请添加</view>
        </template>
        <template v-else>
          <wd-tag
            v-for="(tag, index) in currentTags"
            :key="index"
            round
            closable
            color="#333"
            :bg-color="getTagColor(tag, index).bgColor"
            @close="removeTag(index)"
          >
            {{ tag }}
          </wd-tag>
        </template>
      </view>
    </view>

    <!-- 添加新标签 -->
    <view class="section-container">
      <view class="section-title">添加新标签</view>
      <view class="input-container">
        <wd-input
          v-model="newTag"
          placeholder="请输入标签名称"
          clearable
          custom-class="tag-input"
        />
        <wd-button
          size="small"
          :disabled="!newTag.trim()"
          @click="addTag"
          custom-class="add-tag-button"
          style="min-width: 60px"
        >
          <view class="i-solar:add-circle-bold w-14px h-14px mr-1rpx"></view>
          添加
        </wd-button>
      </view>
    </view>

    <!-- 历史标签 -->
    <view class="section-container">
      <view class="section-title">本地标签库</view>
      <view class="tags-container">
        <template v-if="historyTags.length === 0">
          <view class="empty-tip">暂无历史标签</view>
        </template>
        <template v-else>
          <wd-tag
            v-for="(tag, index) in historyTags"
            :key="index"
            round
            closable
            color="#333"
            :bg-color="getTagColor(tag, index).bgColor"
            @click="selectHistoryTag(tag)"
            @close="removeHistoryTag(index)"
          >
            {{ tag }}
          </wd-tag>
        </template>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-btn-container">
      <wd-button type="primary" block custom-class="cut-button" @click="confirmTags">
        确认
      </wd-button>
    </view>
    <wd-gap safe-area-bottom height="0"></wd-gap>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useToast } from 'wot-design-uni'
import { useTagStore } from '@/store/tag'
import { getTagColorByContent } from '@/utils/tag-colors'
import { onShow } from '@dcloudio/uni-app'

// 初始化toast
const toast = useToast()

// 获取标签store
const tagStore = useTagStore()

// 当前标签
const currentTags = ref<string[]>([])

// 新标签输入
const newTag = ref('')

// 历史标签 (从store中获取)
const historyTags = ref<string[]>([])

// 用于存储需要更新到本地存储的标签
const tagsToUpdate = ref<string[]>([])

// 事件通道
let eventChannel: any = null

// 获取标签颜色
const getTagColor = (tag: string, index: number) => {
  return getTagColorByContent(tag, index)
}

// 页面加载时获取传递的标签数据
onShow(() => {
  // 初始化标签store
  tagStore.initTags()

  // 获取历史标签
  updateHistoryTagsFromStore()

  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-expect-error 获取事件通道
  eventChannel = currentPage.getOpenerEventChannel()

  // 监听初始化事件
  eventChannel.on('initTags', (data: { tags: string[] }) => {
    if (data && Array.isArray(data.tags)) {
      currentTags.value = [...data.tags]
      // 不再从历史标签中移除当前已选标签
    }
  })
})

// 从store更新历史标签 - 创建快照而不是直接引用
const updateHistoryTagsFromStore = () => {
  // 创建本地标签库的快照，而不是直接引用
  historyTags.value = [...tagStore.getAllTags()]
}

// 不再需要单独的updateHistoryTags函数

// 添加新标签
const addTag = () => {
  const tag = newTag.value.trim()
  if (!tag) return

  // 检查是否已存在
  if (currentTags.value.includes(tag)) {
    toast.warning('该标签已选中')
    return
  }

  // 添加到当前标签
  currentTags.value.push(tag)

  // 添加到历史标签store - 这里仍然立即添加，因为这是新标签
  tagStore.addTag(tag)

  // 更新历史标签列表
  updateHistoryTagsFromStore()

  // 清空输入框
  newTag.value = ''

  toast.success('添加成功')
}

// 移除标签
const removeTag = (index: number) => {
  // 仅从当前标签中删除，不影响历史标签
  currentTags.value.splice(index, 1)
  toast.success('已移除')
}

// 选择历史标签
const selectHistoryTag = (tag: string) => {
  // 检查是否已存在
  if (currentTags.value.includes(tag)) {
    toast.warning('该标签已存在')
    return
  }

  // 添加到当前标签
  currentTags.value.push(tag)

  // 将标签添加到待更新列表，而不是立即更新本地存储
  if (!tagsToUpdate.value.includes(tag)) {
    tagsToUpdate.value.push(tag)
  }
}

// 移除历史标签
const removeHistoryTag = (index: number) => {
  const tag = historyTags.value[index]
  tagStore.removeTag(tag)
  updateHistoryTagsFromStore()
  toast.success('已删除')
}

// 确认标签选择
const confirmTags = () => {
  // 更新所有选择的标签到本地存储
  tagsToUpdate.value.forEach((tag) => {
    tagStore.useTag(tag)
  })

  // 通过事件通道将标签数据传回上一页
  if (eventChannel) {
    eventChannel.emit('updateTags', { tags: currentTags.value })
  }

  // 返回上一页
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.tag-manager-container {
  min-height: 100vh;
  padding-top: 16px;
  padding-right: 32rpx;
  padding-left: 32rpx;
  background-color: #f8f8f8;
}

.section-container {
  padding: 24rpx;
  margin-bottom: 40rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  margin-bottom: 20rpx;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  position: relative;
  display: flex;
  align-items: center;
}

.empty-tip {
  width: 100%;
  padding: 20rpx 0;
  font-size: 14px;
  color: #999;
  text-align: center;
}

.input-container {
  display: flex;
  gap: 16rpx;
  align-items: center;
  padding: 4rpx 0;
}

:deep(.tag-input) {
  flex: 1;
  height: 48rpx !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 8px !important;
}

:deep(.add-tag-button) {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 48rpx !important;
  padding: 0 10rpx !important;
  overflow: hidden !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  color: #337ea9 !important;
  background-color: #e7f3f8 !important;
  border: 1px solid rgba(51, 126, 169, 0.5) !important;
  border-radius: 8px !important;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05) !important;
  transition: all 0.2s ease !important;
}

:deep(.add-tag-button:active) {
  border-radius: 8px !important;
  transform: scale(0.95) !important;
}

:deep(.add-tag-button:active::before) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: -1 !important;
  width: 100% !important;
  height: 100% !important;
  content: '' !important;
  background-color: inherit !important;
  border-radius: 8px !important;
}

:deep(.add-tag-button[disabled]) {
  color: #999 !important;
  background-color: #f0f0f0 !important;
  border: 1px solid #e0e0e0 !important;
  opacity: 0.6 !important;
}

.delete-icon {
  margin-left: 8rpx;
}

.bottom-btn-container {
  position: fixed;
  right: 32rpx;
  bottom: 32rpx;
  left: 32rpx;
  z-index: 10;
}

:deep(.cut-button) {
  width: 690rpx !important;
  height: 80rpx !important;
  margin-top: 16px !important;
  margin-bottom: 20px !important;
  font-size: 16px !important;
  line-height: 24px !important;
  border: 0.1rpx solid #337ea9 !important;
  border-radius: 12px !important;
  box-shadow: #edeef1 0px 1px 7px 0px;
}
</style>
