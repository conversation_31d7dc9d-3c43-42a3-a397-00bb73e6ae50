/* 弹出层样式 */
:deep(.tag-popup) {
  height: 75vh;
  border-radius: 24px 24px 0 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.08);
}

:deep(.remark-popup) {
  border-radius: 24px 24px 0 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.08);
  transition: height 0.3s ease;
}

.tag-popup-container,
.remark-popup-container {
  display: flex;
  flex-direction: column;
  height: 80vh;
  padding: 24rpx 0 0; /* 添加上边距，为圆角留出空间 */
  overflow: hidden;
  background-color: #fff;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* 改为右对齐 */
  padding: 0 0 24rpx;
  margin-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  flex: 1;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  text-align: center;
}
/* 移除备注标题栏样式 */

.remark-title {
  font-size: 16px;
  font-weight: 400;
  color: #666;
}

.popup-action {
  min-width: 80rpx;
  padding: 8rpx 0; /* 移除水平内边距 */
  margin-right: 32rpx; /* 添加右边距，与当前标签文字对齐 */
  text-align: right;
}

.action-text {
  font-size: 16px;
  font-weight: 500;
  color: #337ea9;
}

.section-container {
  padding: 24rpx 0;
  margin-bottom: 28rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 两端对齐，为已选择计数留出空间 */
  margin-right: 32rpx; /* 增加右边距 */
  margin-bottom: 20rpx;
  margin-left: 32rpx; /* 增加左边距 */
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 4rpx 32rpx; /* 增加左右边距，确保与页面边缘保持足够距离 */
}

.empty-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(100% - 48rpx); /* 调整宽度以考虑左右边距 */
  padding: 24rpx 0;
  margin: 0 24rpx; /* 添加左右边距 */
  font-size: 14px;
  color: #999;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 12rpx;
}

:deep(.enhanced-tag) {
  position: relative !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: auto !important;
  min-height: 48rpx !important;
  padding: 6rpx 16rpx !important; /* 增加上下内边距 */
  margin: 4rpx !important;
  overflow: hidden !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
  border-radius: 8px !important; /* 减小圆角大小 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05) !important;
  transition: all 0.2s ease !important;
}
/* 已选中标签的样式 */
:deep(.selected-tag) {
  overflow: hidden !important;
  font-weight: 500 !important;
  color: #337ea9 !important;
  background-color: #e7f3f8 !important;
  border: 1px solid rgba(51, 126, 169, 0.5) !important;
  border-radius: 8px !important;
}
/* 未选中标签的样式 */
:deep(.unselected-tag) {
  overflow: hidden !important;
  font-weight: normal !important;
  color: #666 !important;
  background-color: #ffffff !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 8px !important;
}
/* 已选择计数样式 */
.selected-count {
  font-size: 14px;
  font-weight: normal;
  color: #337ea9;
}
/* 标签内文本样式 */
:deep(.enhanced-tag .wd-tag__text) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  padding: 2rpx 0 !important; /* 添加上下内边距，确保文字垂直居中 */
  line-height: normal !important; /* 使用normal行高，避免文字被压缩 */
  vertical-align: middle !important;
}

:deep(.enhanced-tag:active) {
  border-radius: 8px !important;
  transform: scale(0.95);
}
/* 确保标签点击时的背景色也有圆角 */
:deep(.enhanced-tag:active::before) {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  content: '';
  background-color: inherit;
  border-radius: 8px !important;
}
/* 标签关闭按钮样式 */
:deep(.enhanced-tag .wd-tag__close) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  padding: 2rpx !important; /* 添加内边距，增加可点击区域 */
  margin-left: 8rpx !important; /* 增加与文字的间距 */
  vertical-align: middle !important;
}

:deep(.history-tag) {
  position: relative !important;
  padding: 0rpx 16rpx !important; /* 增加上下内边距 */
  overflow: hidden !important;
  vertical-align: middle !important;
  cursor: pointer !important;
}

.input-container {
  display: flex;
  gap: 16rpx;
  align-items: center;
  padding: 4rpx 32rpx; /* 增加左右边距，与标题对齐 */
}

:deep(.tag-input) {
  flex: 1;
  border-radius: 8rpx !important;
}

:deep(.add-tag-button) {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 60rpx !important;
  padding: 0 16rpx !important;
  overflow: hidden !important;
  font-weight: 500 !important;
  border-radius: 8rpx !important;
  box-shadow: 0 2rpx 8rpx rgba(51, 126, 169, 0.15) !important;
}

:deep(.add-tag-button .wd-button__text) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
}

:deep(.add-tag-button:active) {
  border-radius: 8px !important;
  transform: scale(0.95) !important;
}

:deep(.add-tag-button:active::before) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: -1 !important;
  width: 100% !important;
  height: 100% !important;
  content: '' !important;
  background-color: inherit !important;
  border-radius: 8px !important;
}

:deep(.add-tag-button[disabled]) {
  color: #999 !important;
  background-color: #f0f0f0 !important;
  border: 1px solid #e0e0e0 !important;
  opacity: 0.6 !important;
}

.editor-section {
  flex: 1;
  padding: 0;
  overflow-y: auto;
}

.editor-area {
  padding: 20rpx 24rpx 16rpx;
}

.button-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20rpx;
  margin-bottom: 8rpx;
}

.button-left {
  display: flex;
  gap: 16rpx;
}

.button-right {
  display: flex;
  gap: 16rpx;
  align-items: center;
  margin-left: auto;
}
/* 移除旧的确认按钮样式 */

:deep(.action-button) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 70rpx !important;
  padding: 0 24rpx !important;
  font-weight: 500 !important;
  color: #666 !important;
  background-color: #f5f7fa !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 8rpx !important;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05) !important;
}

:deep(.remark-input) {
  position: relative;
  width: 100%;
  min-height: 220rpx;
  max-height: 400rpx;
  padding: 16rpx 24rpx;
  margin: 0;
  font-size: 15px !important;
  line-height: 1.5 !important;
  background-color: #fff;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none;
}

:deep(.remark-input .wd-textarea__word-count) {
  position: absolute;
  right: 12rpx;
  bottom: 12rpx;
  font-size: 12px;
  color: #999;
}

:deep(.remark-input:focus) {
  border-color: #337ea9 !important;
  box-shadow: inset 0 1px 3px rgba(51, 126, 169, 0.1);
}
/* 底部按钮样式已移除，改为顶部确认按钮 */
/* 分隔线样式 */
.divider {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40rpx;
  margin: 24rpx 0;
  text-align: center;
}

.divider::before,
.divider::after {
  flex: 1;
  height: 1px;
  content: '';
  background-color: #e8e8e8;
}

.divider-text {
  padding: 0 16rpx;
  font-size: 14px;
  font-weight: 500;
  color: #999;
}

.empty-tags-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 24rpx 0;
  font-size: 14px;
  color: #999;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 12rpx;
}

.bottom-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 24rpx;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.clear-button,
.clipboard-button {
  padding: 8rpx 16rpx;
  font-size: 14px;
  color: #ff6b6b;
  background-color: transparent;
}

.clipboard-button {
  color: #3498db;
}

.confirm-button {
  margin-right: 16rpx;
  font-size: 16px;
  font-weight: 400;
  color: #337ea9;
}
