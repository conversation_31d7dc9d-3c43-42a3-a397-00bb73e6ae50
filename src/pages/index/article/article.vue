<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '文章剪藏',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="overflow-hidden bg-white">
    <view class="flex flex-col items-center justify-start w-full relative bg-white min-h-screen">
      <!-- 顶部封面图 -->
      <wd-img
        width="100%"
        height="380rpx"
        mode="aspectFill"
        :enable-preview="true"
        :src="article.cover"
      />

      <!-- 悬浮头像容器 -->
      <view class="avatar-container">
        <wd-img
          width="60"
          height="60"
          mode="aspectFill"
          :src="article.icon"
          custom-class="avatar-image"
        />
      </view>

      <!-- 文章标题区域，添加上边距为头像留出空间 -->
      <view class="w-full flex flex-row items-center justify-start pt-[40px]">
        <view class="mx-4 w-full">
          <textarea
            v-model="article.title"
            class="custom-textarea"
            :maxlength="100"
            :auto-height="true"
          />
        </view>
      </view>

      <view class="w-full mt-4">
        <wd-row custom-class="mx-4">
          <wd-col :span="6">
            <view class="line-height-30px flex flex-row items-center justify-start">
              <view class="i-solar:user-speak-rounded-outline w-20px h-20px c-#555"></view>
              <wd-text text="作者" size="16px" custom-class="ml-12rpx" color="#666" />
            </view>
          </wd-col>
          <wd-col :span="18">
            <view class="line-height-30px">
              <view
                class="custom-tag !text-size-15px"
                style="color: #347ea9; background-color: #e7f3f8"
              >
                {{ article.author }}
              </view>
            </view>
          </wd-col>
        </wd-row>
        <wd-gap height="8px"></wd-gap>
        <wd-row custom-class="mx-4">
          <wd-col :span="6">
            <view class="line-height-30px flex flex-row items-center justify-start">
              <view class="i-solar:link-minimalistic-2-linear w-20px h-20px c-#555"></view>
              <wd-text text="链接" size="16px" custom-class="ml-12rpx" color="#666" />
            </view>
          </wd-col>
          <wd-col :span="18">
            <view class="line-height-30px">
              <!-- 域名部分 -->
              <text class="c-gray-700">{{ formattedLink.domain }}</text>
              <!-- 路径部分 -->
              <text class="c-gray-400">{{ formattedLink.path }}</text>
            </view>
          </wd-col>
        </wd-row>
        <wd-gap height="8px"></wd-gap>
        <wd-row custom-class="mx-4">
          <wd-col :span="6">
            <view class="line-height-30px flex flex-row items-center justify-start">
              <view class="i-solar:tag-linear w-20px h-20px c-#555"></view>
              <wd-text text="标签" size="16px" custom-class="ml-12rpx" color="#666" />
            </view>
          </wd-col>
          <wd-col :span="18">
            <view class="line-height-30px tags-container">
              <!-- 当没有标签时显示默认标签 -->
              <template v-if="article.tags.length === 0">
                <view
                  class="custom-tag !text-size-15px tag-item add-tag-btn"
                  style="color: #666666; background-color: #f0f0f0"
                  @click="showTagPopup = true"
                >
                  添加标签
                </view>
              </template>
              <!-- 当有标签时循环显示 -->
              <template v-else>
                <view
                  v-for="(tag, index) in article.tags"
                  :key="index"
                  class="custom-tag !text-size-15px tag-item"
                  :style="{ color: '#555', backgroundColor: getTagColor(tag, index).bgColor }"
                >
                  {{ tag }}
                  <view class="tag-remove" @click.stop="removeArticleTag(index)">
                    <view class="i-solar:close-circle-bold w-14px h-14px c-#666 ml-4rpx"></view>
                  </view>
                </view>
                <view
                  class="custom-tag !text-size-15px tag-item add-more-tag"
                  style="color: #666666; background-color: #f0f0f0"
                  @click="showTagPopup = true"
                >
                  <view class="i-solar:add-circle-bold w-14px h-14px c-#666 mr-4rpx"></view>
                  更多
                </view>
              </template>
            </view>
          </wd-col>
        </wd-row>
        <wd-gap height="8px"></wd-gap>
        <wd-row custom-class="mx-4">
          <wd-col :span="6">
            <view class="line-height-30px flex flex-row items-center justify-start">
              <view class="i-solar:pen-new-round-linear w-20px h-20px c-#555"></view>
              <wd-text text="备注" size="16px" custom-class="ml-12rpx" color="#666" />
            </view>
          </wd-col>
          <wd-col :span="18">
            <view class="line-height-30rpx remark-container" @click="showRemarkPopup = true">
              <!-- 当没有备注时显示提示文本 -->
              <template v-if="!article.remark">
                <view class="empty-remark">点击添加备注</view>
              </template>
              <!-- 当有备注时显示备注内容 -->
              <template v-else>
                <view class="remark-text">{{ article.remark }}</view>
              </template>
            </view>
          </wd-col>
        </wd-row>

        <!-- 分隔线 -->
        <view v-if="recentTags.length > 0" class="divider mx-4 my-3">
          <view class="divider-text">常用标签</view>
        </view>

        <!-- 快速选择标签区域 -->
        <view v-if="recentTags.length > 0" class="quick-tags-container mx-4 mb-4">
          <wd-tag
            v-for="(tag, index) in recentTags"
            :key="index"
            round
            :color="isTagInArticle(tag) ? '#337ea9' : '#666'"
            :bg-color="isTagInArticle(tag) ? '#e7f3f8' : '#f0f0f0'"
            :custom-class="`quick-tag ${isTagInArticle(tag) ? 'selected-tag' : 'unselected-tag'}`"
            @click="quickAddTag(tag)"
          >
            {{ tag }}
          </wd-tag>
        </view>
      </view>
      <wd-gap safe-area-bottom height="0"></wd-gap>
    </view>
    <!-- 底部按钮 -->
    <view class="bottom-btn-container">
      <wd-button type="primary" block custom-class="cut-button" @click="handleSaveArticle">
        保存到 Notion
      </wd-button>
    </view>
    <wd-gap safe-area-bottom height="0"></wd-gap>

    <!-- 标签管理弹出层 -->
    <wd-popup v-model="showTagPopup" position="bottom" round custom-class="tag-popup">
      <view class="tag-popup-container">
        <!-- 标题栏 -->
        <view class="popup-header">
          <view class="popup-action" @click="showTagPopup = false">
            <text class="action-text confirm-text">确认</text>
          </view>
        </view>

        <!-- 添加新标签 -->
        <view class="section-container">
          <view class="section-title">
            <text>添加新标签</text>
          </view>
          <view class="input-container">
            <wd-input
              v-model="newTag"
              placeholder="请输入标签名称"
              clearable
              custom-class="tag-input"
            />
            <wd-button
              size="small"
              :disabled="!newTag.trim()"
              @click="addTag"
              custom-class="add-tag-button"
            >
              <view class="i-solar:add-circle-bold w-16px h-16px mr-1"></view>
              添加
            </wd-button>
          </view>
        </view>

        <!-- 标签库 -->
        <view class="section-container">
          <view class="section-title">
            <text>本地标签库</text>
            <view class="selected-count" v-if="currentTags.length > 0">
              <text>已选择 {{ currentTags.length }} 个</text>
            </view>
          </view>
          <view class="tags-container-popup">
            <template v-if="historyTags.length === 0">
              <view class="empty-tip">
                <text>暂无历史标签</text>
              </view>
            </template>
            <template v-else>
              <wd-tag
                v-for="(tag, index) in historyTags"
                :key="index"
                round
                :color="isTagSelected(tag) ? '#337ea9' : '#666'"
                :custom-class="`enhanced-tag history-tag ${isTagSelected(tag) ? 'selected-tag' : 'unselected-tag'}`"
                :bg-color="isTagSelected(tag) ? '#e7f3f8' : '#ffffff'"
                @click="toggleTagSelection(tag)"
                closable
                @close="removeHistoryTag(index)"
              >
                {{ tag }}
              </wd-tag>
            </template>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 备注编辑弹出层 -->
    <wd-popup
      v-model="showRemarkPopup"
      position="bottom"
      round
      custom-class="remark-popup"
      :mask-closable="false"
      :style="remarkPopupStyle"
    >
      <view class="remark-popup-container">
        <!-- 编辑区域 -->
        <view class="editor-section">
          <!-- 底部按钮区域 -->
          <view class="bottom-buttons">
            <view class="button-left">
              <view class="clear-button" @click="clearContent">清空</view>
            </view>
            <view class="button-right">
              <view class="clipboard-button" @click="getClipboardContent">读取剪贴板</view>
              <view class="confirm-button" @click="showRemarkPopup = false">确认</view>
            </view>
          </view>
          <view class="editor-area">
            <wd-textarea
              v-model="remarkContent"
              placeholder="请输入备注内容..."
              :maxlength="500"
              :rows="6"
              custom-class="remark-input"
              auto-height
              no-border
            />
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { ref, computed, watch } from 'vue'
import { useToast, useMessage } from 'wot-design-uni'
import { getTagColorByContent } from '@/utils/tag-colors'
import { extractErrorMessage } from '@/utils'
import { resolveLink, UrlRQ, saveArticle, ArticleBody } from '@/service/clip/article'
import { useTagStore } from '@/store/tag'
import { useUserStore } from '@/store'

const toast = useToast()
const message = useMessage()

// 获取标签store
const tagStore = useTagStore()

// 获取用户store
const userStore = useUserStore()

// 标签管理弹出层状态
const showTagPopup = ref(false)
// 备注编辑弹出层状态
const showRemarkPopup = ref(false)

// 监听备注弹出层状态变化
watch(showRemarkPopup, (newVal) => {
  if (newVal) {
    // 弹出层显示时，确保键盘监听已设置
    setupKeyboardListeners()
  } else {
    // 弹出层关闭时，重置键盘高度
    keyboardHeight.value = 0
  }
})

// 当前标签（用于弹出层内部管理）
const currentTags = ref<string[]>([])
// 新标签输入
const newTag = ref('')
// 历史标签
const historyTags = ref<string[]>([])
// 用于存储需要更新到本地存储的标签
const tagsToUpdate = ref<string[]>([])
// 最近使用的标签（快速选择区域显示）
const recentTags = ref<string[]>([])

// 备注内容
const remarkContent = ref('')

// 键盘高度
const keyboardHeight = ref(0)
// 是否已经注册了键盘事件
const keyboardEventsRegistered = ref(false)

// 弹出层高度样式
const remarkPopupStyle = computed(() => {
  // 如果键盘显示，设置为适应键盘的高度
  if (keyboardHeight.value > 0) {
    return {
      height: `calc(100vh - ${keyboardHeight.value}px - 20px)`,
      bottom: `${keyboardHeight.value}px`,
      transition: 'bottom 0.3s',
    }
  }
  // 如果键盘隐藏，设置为固定高度
  return {
    height: '480rpx',
    bottom: '0px',
    transition: 'bottom 0.3s',
  }
})

// 监听键盘高度变化
const setupKeyboardListeners = () => {
  // 避免重复注册事件
  if (keyboardEventsRegistered.value) return

  // 监听键盘弹出事件
  uni.onKeyboardHeightChange((res) => {
    console.log('键盘高度变化：', res.height)
    keyboardHeight.value = res.height
  })

  keyboardEventsRegistered.value = true
}

// 创建统一的文章对象，与 ArticleBody 结构保持一致
const article = ref<ArticleBody>({
  title: '文章标题-可编辑',
  link: 'https://www.example.com/article',
  author: '文章作者',
  origin: '文章来源',
  tags: [],
  remark: '',
  publishTime: '',
  createTime: new Date().toISOString(),
  cover:
    'https://res.cloudinary.com/dwal1nlws/image/upload/v1731799143/wexin/mp/63d9e30d692ce7ca5f71363c4ae7f578.png',
  icon: 'https://res.cloudinary.com/dwal1nlws/image/upload/v1731164499/wexin/mp/da8b9725c2dc50080c4ca5e68054a594.png',
  siteName: '',
  description: '',
})

// 从store更新历史标签 - 创建快照而不是直接引用
const updateHistoryTagsFromStore = () => {
  // 创建本地标签库的快照，而不是直接引用
  // 显示所有本地标签
  historyTags.value = [...tagStore.getAllTags()]
}

// 初始化标签管理弹出层
const initTagPopup = () => {
  // 初始化标签store
  tagStore.initTags()

  // 获取历史标签
  updateHistoryTagsFromStore()

  // 复制当前文章标签到弹出层的当前标签
  currentTags.value = [...article.value.tags]

  // 清空新标签输入和待更新列表
  newTag.value = ''
  tagsToUpdate.value = []
}

// 初始化最近使用的标签
const initRecentTags = () => {
  // 获取最近使用的标签（最多显示20个）
  tagStore.initTags()
  recentTags.value = tagStore.getAllTags().slice(0, 20)
}

// 快速添加标签
const quickAddTag = (tag: string) => {
  // 检查是否已存在
  if (article.value.tags.includes(tag)) {
    return
  }

  // 添加到文章标签
  article.value.tags.push(tag)

  // 将标签添加到待更新列表，而不是立即更新本地存储
  // 这样可以避免在用户交互过程中改变标签顺序
  if (!tagsToUpdate.value.includes(tag)) {
    tagsToUpdate.value.push(tag)
  }

  // 不再立即更新最近标签列表，避免顺序变化
  // 标签顺序将在弹出层关闭或页面加载时更新
}

// 从文章中移除标签
const removeArticleTag = (index: number) => {
  article.value.tags.splice(index, 1)
}

// 添加新标签
const addTag = () => {
  const tag = newTag.value.trim()
  if (!tag) return

  // 检查是否已存在
  if (currentTags.value.includes(tag)) {
    return
  }

  // 添加到当前标签
  currentTags.value.push(tag)

  // 添加到历史标签store - 这里仍然立即添加，因为这是新标签
  tagStore.addTag(tag)

  // 更新历史标签列表
  updateHistoryTagsFromStore()

  // 清空输入框
  newTag.value = ''
}

// 检查标签是否已选中
const isTagSelected = (tag: string) => {
  return currentTags.value.includes(tag)
}

// 切换标签选中状态
const toggleTagSelection = (tag: string) => {
  if (isTagSelected(tag)) {
    // 如果已选中，则移除
    const index = currentTags.value.indexOf(tag)
    if (index !== -1) {
      currentTags.value.splice(index, 1)
      toast.success('已取消选择')
    }
  } else {
    // 如果未选中，则添加
    currentTags.value.push(tag)

    // 将标签添加到待更新列表，而不是立即更新本地存储
    if (!tagsToUpdate.value.includes(tag)) {
      tagsToUpdate.value.push(tag)
    }

    toast.success('已选择')
  }
}

// 移除历史标签
const removeHistoryTag = (index: number) => {
  const tag = historyTags.value[index]

  // 从历史标签库中移除
  tagStore.removeTag(tag)
  updateHistoryTagsFromStore()

  // 如果标签已被选中，也从当前选中标签中移除
  const selectedIndex = currentTags.value.indexOf(tag)
  if (selectedIndex !== -1) {
    currentTags.value.splice(selectedIndex, 1)
  }

  toast.success('已删除')
}

// 确认标签选择
const confirmTags = () => {
  // 更新文章对象的标签
  article.value.tags = [...currentTags.value]

  // 关闭弹出层
  showTagPopup.value = false
}

// 初始化备注编辑弹出层
const initRemarkPopup = () => {
  setupKeyboardListeners()
  // 复制当前文章备注到弹出层的备注内容
  remarkContent.value = article.value.remark || ''
}

// 读取剪贴板内容
const getClipboardContent = () => {
  uni.getClipboardData({
    success: (res) => {
      if (res.data) {
        remarkContent.value = res.data
      } else {
        toast.warning('剪贴板内容为空')
      }
    },
    fail: () => {},
  })
}

// 清空内容
const clearContent = () => {
  if (remarkContent.value) {
    remarkContent.value = ''
  }
}

// 确认备注内容
const confirmRemark = () => {
  // 更新文章对象的备注
  article.value.remark = remarkContent.value

  // 关闭弹出层
  showRemarkPopup.value = false
}

// 格式化链接的计算属性
const formattedLink = computed(() => {
  // 使用文章对象中的链接
  const url = article.value.link

  // 移除 http:// 或 https:// 前缀
  const cleanUrl = url.replace(/^https?:\/\//, '')

  // 分离域名和路径
  let domain = cleanUrl
  let path = ''

  // 查找第一个斜杠的位置
  const slashIndex = cleanUrl.indexOf('/')

  if (slashIndex !== -1) {
    // 如果有路径，则分割
    domain = cleanUrl.substring(0, slashIndex)
    path = cleanUrl.substring(slashIndex)

    // 如果路径过长，使用省略号并保留尾部4个字符
    if (path.length > 15) {
      const lastFourChars = path.slice(-4)
      path = path.substring(0, 8) + '...' + lastFourChars
    }
  }

  // 只返回格式化后的链接信息用于展示，不进行修改和逻辑处理

  return {
    domain,
    path,
  }
})

// 获取标签颜色
const getTagColor = (tag: string, index: number) => {
  return getTagColorByContent(tag, index)
}

// 检查标签是否已添加到文章中
const isTagInArticle = (tag: string) => {
  return article.value.tags.includes(tag)
}

// 保存文章
const handleSaveArticle = async () => {
  if (
    !article.value.title ||
    !article.value.link ||
    article.value.link === 'https://www.example.com/article'
  ) {
    toast.error('解析失败，请重新解析')
    return
  }
  try {
    toast.loading('保存中...')

    // 确保创建时间是最新的
    article.value.createTime = new Date().toISOString()

    // 直接使用文章对象作为请求数据
    // 调用保存文章接口
    const { data, msg } = await saveArticle(article.value)
    const errMsg = msg

    // 关闭加载提示
    toast.close()

    if (data) {
      // 保存成功后，更新所有标签的使用频率
      // 包括快速添加的标签和弹出层选择的标签
      article.value.tags.forEach((tag) => {
        tagStore.useTag(tag)
      })

      // 更新最近标签列表
      initRecentTags()

      // 清空待更新列表
      tagsToUpdate.value = []

      // 设置需要刷新剪藏数据的标记
      userStore.setNeedRefreshClipData(true)

      // 保存成功后显示确认框
      message
        .confirm({
          title: '保存成功',
          msg: '文章已保存到 Notion',
          confirmButtonText: '关闭',
          cancelButtonText: '返回首页',
        })
        .then(() => {
          // 点击取消后关闭小程序
          uni.exitMiniProgram()
        })
        .catch(() => {
          // 点击确认后跳转到首页
          uni.switchTab({
            url: '/pages/index/index',
          })
        })
    } else {
      // 保存失败后显示确认框
      message
        .confirm({
          title: '保存失败',
          msg: errMsg,
          confirmButtonText: 'OK',
          cancelButtonText: '去反馈',
        })
        .then(() => {
          // 停留在当前页面
        })
        .catch(() => {
          // 点击确认后跳转到首页
          uni.switchTab({
            url: '/pages/about/feedback/feedback',
          })
        })
    }
  } catch (error) {
    console.error('保存文章失败:', error)

    // 使用全局错误提取方法
    const errorMessage = extractErrorMessage(error)

    // 关闭加载提示
    toast.close()

    // 显示错误提示
    toast.error('保存失败: ' + errorMessage)
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 监听标签弹出层显示
watch(showTagPopup, (newVal) => {
  if (newVal) {
    // 当弹出层显示时初始化
    initTagPopup()
  } else {
    // 当弹出层关闭时，如果有标签变化，自动确认
    // 比较当前标签和文章标签是否一致
    const articleTags = article.value.tags.slice().sort().join(',')
    const popupTags = currentTags.value.slice().sort().join(',')

    if (articleTags !== popupTags) {
      // 标签有变化，自动确认
      confirmTags()
    }
  }
})

// 监听备注弹出层显示
watch(showRemarkPopup, (newVal) => {
  if (newVal) {
    // 当弹出层显示时初始化
    initRemarkPopup()
  } else {
    // 当弹出层关闭时，如果备注有变化，自动确认
    // 比较当前备注和文章备注是否一致
    const articleRemark = article.value.remark || ''
    const popupRemark = remarkContent.value || ''

    if (articleRemark !== popupRemark) {
      // 备注有变化，自动确认
      confirmRemark()
    }

    // 重置键盘高度
    keyboardHeight.value = 0
  }
})

// 初始化数据
const initData = () => {
  // 初始化最近使用的标签
  initRecentTags()
}

onLoad(async (option) => {
  // 初始化数据
  initData()
  // 如果有文章数据参数，先进行URL解码并解析JSON
  if (option.articleData) {
    try {
      // 解码并解析文章数据
      const articleDataStr = decodeURIComponent(option.articleData)
      const articleData = JSON.parse(articleDataStr)
      console.log('解析到的文章数据:', articleData)

      // 使用解析结果替换整个文章对象
      if (articleData) {
        // 将解析的数据合并到文章对象中，保留原有值作为默认值
        article.value = {
          ...article.value,
          ...articleData,
        }
      }
    } catch (error) {
      console.error('解析文章数据失败:', error)
      toast.error('解析文章数据失败')
    }
  } else {
    const options = uni.getEnterOptionsSync() as any
    if (
      options.scene === 1173 &&
      options.forwardMaterials &&
      options.forwardMaterials[0] &&
      options.forwardMaterials[0].path
    ) {
      console.log('获取到的转发数据:', options)
      const linkA = decodeURIComponent(options.forwardMaterials[0].path)
      try {
        toast.loading('解析链接中...')

        // 准备请求数据
        const linkData: UrlRQ = {
          url: linkA,
        }

        // 调用解析链接接口
        const { data, msg } = await resolveLink(linkData)

        toast.close()

        if (data) {
          // 这里要判断如果解析的文章标题内容为空，则提示解析失败
          if (!data.title) {
            console.error('解析的文章标题为空')
            // 使用 MessageBox 确认框
            // 使用 Promise 方式处理确认框的点击事件
            message
              .alert({
                title: '解析失败',
                msg: '请重试一下或反馈给开发者看看',
                confirmButtonText: 'OK',
              })
              .then(() => {
                // 点击确认后跳转到首页
                uni.switchTab({
                  url: '/pages/index/index',
                })
              })
            return
          }
          console.log('解析到的文章数据:', data)

          // 直接使用解析结果更新文章对象
          article.value = {
            ...article.value,
            ...data,
          }
          toast.success('解析成功')
        } else {
          // 解析失败，显示错误信息
          toast.error(msg || '解析链接失败')
        }
      } catch (error) {
        // 使用全局错误提取方法
        const errorMessage = extractErrorMessage(error)
        toast.close()

        toast.error('解析失败: ' + errorMessage)
      } finally {
        // 关闭加载提示
        setTimeout(() => {
          toast.close()
        }, 500) // 等待一段时间再关闭，确保错误提示能够显示
      }
    }
  }
})
</script>

<style lang="scss" scoped>
/* 弹出层样式 - 引入外部CSS文件 */
@import url('./article-popup.css');

page {
  height: 100%;
  min-height: 100vh;
  background-color: #fff;
}

.overflow-hidden {
  height: 100%;
  min-height: 100vh;
  background-color: #fff;
}

.avatar-container {
  position: absolute;
  top: 321rpx;
  /* 头像上半部分覆盖在封面图上，下半部分在内容区域 */
  left: 16px;
  /* 左边距离 */
  z-index: 10;
  /* 确保头像在最上层 */
}

.avatar-image {
  border: 2px solid #ffffff;
  border-radius: 8px;
}

.bg-dark1,
.bg-dark,
.bg-light {
  min-height: 30px;
  margin-bottom: 10px;
  font-size: 12px;
  line-height: 30px;
  // color: rgba(0, 0, 0, 0.45);
  text-align: center;
  border-radius: 4px;
}

.bg-dark1 {
  color: #fff;
  background: #99a9bf;
}

.bg-dark {
  background: #d3dce6;
}

.bg-light {
  background: #e5e9f2;
}

:deep(.text-input) {
  width: auto;
  padding: 10rpx;
  background-color: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}
/* 自定义文章标题文本域样式 */
.custom-textarea {
  width: 100%;
  padding: 0;
  font-size: 20px;
  font-weight: bold;
  line-height: 30px;
  color: #333;
  text-align: left;
  resize: none;
  background-color: transparent;
  border: none;
  outline: none;
  /* 非焦点状态下的样式 */
  &:not(:focus) {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    /* 限制为两行 */
    -webkit-box-orient: vertical;
  }
}

.link-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  font-size: 15px;
  word-break: break-all;
}

.domain-text {
  font-weight: 500;
  color: #0066cc;
}

.path-text {
  color: #666666;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 4px 0;
}

.tags-container-popup {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 2px 24rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  margin-right: 0 !important;
  margin-bottom: 0 !important;
}

.tag-remove {
  display: flex;
  align-items: center;
  margin-left: 4rpx;
}

.add-tag-btn,
.add-more-tag {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.add-more-tag {
  display: flex;
  align-items: center;
}

.quick-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  padding: 8rpx 0;
}

:deep(.quick-tag) {
  padding: 2rpx 12rpx !important;
  margin: 0 !important;
  font-size: 14px !important;
  cursor: pointer;
  border-radius: 8rpx !important;
}

:deep(.quick-tag.selected-tag) {
  font-weight: 500 !important;
  color: #337ea9 !important;
  background-color: #e7f3f8 !important;
  border: 1px solid rgba(51, 126, 169, 0.5) !important;
}

:deep(.quick-tag.unselected-tag) {
  font-weight: normal !important;
  color: #666 !important;
  background-color: #f0f0f0 !important;
  border: 1px solid #e0e0e0 !important;
}
/* 自定义标签样式，模拟 wd-tag 的外观 */
.custom-tag {
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 12rpx;
  margin-right: 8px;
  font-size: 15px;
  line-height: 1.5;
  white-space: nowrap;
  border-radius: 4px;
}

.remark-container {
  min-height: 80rpx;
  padding: 12rpx 16rpx;
  cursor: pointer;
  background-color: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.empty-remark {
  font-size: 15px;
  color: #999;
}

.remark-text {
  box-sizing: border-box;
  display: block;
  width: 100%;
  font-size: 15px;
  line-height: 1.5;
  color: #333;
  text-align: left;
  word-break: break-all;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

.bottom-btn-container {
  position: fixed;
  right: 32rpx;
  bottom: 32rpx;
  left: 32rpx;
  z-index: 10;
}

:deep(.cut-button) {
  width: 690rpx !important;
  height: 80rpx !important;
  margin-top: 16px !important;
  margin-bottom: 20px !important;
  font-size: 16px !important;
  line-height: 24px !important;
  border: 0.1rpx solid #337ea9 !important;
  border-radius: 12px !important;
  box-shadow: #edeef1 0px 1px 7px 0px;
}
</style>
