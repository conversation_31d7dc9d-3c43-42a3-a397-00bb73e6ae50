<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '产品新闻',
  },
}
</route>

<template>
  <web-view :src="link"></web-view>
</template>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'

const link = ref('https://mp.weixin.qq.com/s/0_YZKXfoG-oqukrQx4u_jQ')

onLoad((option) => {
  link.value = option.link
  console.log('link:', link.value)
})
</script>

<style lang="scss" scoped>
//
</style>
