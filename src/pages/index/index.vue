<!-- 使用 type="home" 属性设置首页,其他页面不需要设置,默认为page；推荐使用json5,更强大,且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: 'NotionMpClipper',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>
<template>
  <view class="overflow-hidden pb-safe">
    <!-- 登录状态提示 -->
    <view v-if="showLoginHint" class="login-hint-bar">
      <view class="login-hint-content">
        <view class="login-hint-spinner"></view>
        <text class="login-hint-text">正在为您准备个性化内容...</text>
      </view>
    </view>

    <view class="w-full flex flex-col items-center justify-center">
      <view
        class="py-5 w-690rpx flex flex-col items-center justify-center bg-white rounded-12px mt-12px tr-shadow"
      >
        <view class="mb-5 c-black text-size-20px">Notion 移动端剪藏助手</view>
        <view class="mb-2 c-#808080 text-size-12px">支持网页剪藏、文字/图片/文件/聊天记录同步</view>
        <view class="flex flex-row items-center justify-center gap-2 w-660rpx">
          <view class="tr-tag bg-#FCE8E9 c-#d44c47 tr-shadow">小红书</view>
          <view class="tr-tag bg-#FAF1D8 c-#cb912f tr-shadow">即刻</view>
          <view class="tr-tag bg-#d44c47 c-#FCE8E9 tr-shadow">少数派</view>
          <view class="flex flex-row items-center justify-start tr-tag bg-#EFEFED tr-shadow">
            <view class="c-#8ACFDB">下</view>
            <view class="c-#DF2D2B">厨</view>
            <view class="c-#F8805A">房</view>
          </view>
          <view class="tr-tag bg-#EAF1EA c-#448361 tr-shadow">豆瓣</view>
          <view class="tr-tag bg-#CAE0F2 c-#337ea9 tr-shadow">通用网页</view>
        </view>
      </view>
      <wd-gap height="16px" custom-class="bg-gray-200"></wd-gap>
      <view class="w-690rpx relative">
        <!-- 右侧渐变遮罩 -->
        <view v-show="showScrollMask" class="scroll-fade-mask"></view>
        <scroll-view
          :scroll-x="true"
          class="card-scroll-view"
          :show-scrollbar="false"
          :enhanced="true"
          :scroll-into-view="'card-' + activeCardIndex"
          @scroll="handleScroll"
        >
          <view class="card-container">
            <view id="card-0" class="card-item" hover-class="card-hover" @click="cookbook">
              <view class="card-content">
                <view class="card-icon-area bg-#EDF3ED">
                  <view class="flex flex-row items-center justify-center">
                    <view
                      class="i-solar:notebook-minimalistic-linear c-#458261 w-40rpx h-40rpx mr-1"
                    ></view>
                    <view class="c-#458261 text-size-15px flex items-center h-40rpx">使用手册</view>
                  </view>
                </view>
                <view class="card-text-area">
                  <view class="card-title">❶ 先看手册</view>
                </view>
              </view>
            </view>

            <view id="card-1" class="card-item" hover-class="card-hover" @click="hotQuestion">
              <view class="card-content">
                <view class="card-icon-area bg-#F4EEEE">
                  <view class="flex flex-row items-center justify-center">
                    <view
                      class="i-solar:signpost-2-line-duotone c-#9F6B53 w-40rpx h-40rpx mr-1"
                    ></view>
                    <view class="c-#9F6B53 text-size-15px flex items-center h-40rpx">常见问题</view>
                  </view>
                </view>
                <view class="card-text-area">
                  <view class="card-title">❷ 再看问题</view>
                </view>
              </view>
            </view>

            <view id="card-2" class="card-item" hover-class="card-hover" @click="letter">
              <view class="card-content">
                <view class="card-icon-area bg-#E7F3F8">
                  <view class="flex flex-row items-center justify-center">
                    <view
                      class="i-solar:letter-opened-line-duotone c-#347EA9 w-40rpx h-40rpx mr-1"
                    ></view>
                    <view class="c-#347EA9 text-size-15px flex items-center h-40rpx">开发者说</view>
                  </view>
                </view>
                <view class="card-text-area">
                  <view class="card-title">❸ 有空再看</view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <wd-button plain icon="pointing-hand" custom-class="cut-button" @click="toQuickClip">
        快捷剪藏
      </wd-button>
      <view class="w-690rpx">
        <view class="flex flex-row items-center justify-between mb-2">
          <view class="flex flex-row items-center">
            <view class="i-solar:bell-outline c-#337ea9 w-16px h-16px mr-1"></view>
            <wd-text text="产品新闻" size="15px" custom-class="font-medium"></wd-text>
          </view>
        </view>
        <view class="news-container">
          <wd-card
            v-for="(news, index) in productNews"
            :key="news.id || index"
            :title="news.title"
            custom-class="tr-shadow !mx-0 !rounded-12px mb-3"
          >
            {{ news.desc }}
            <template #footer>
              <wd-button size="small" plain hairline @click="openPage(news.link)">
                查看详情
              </wd-button>
            </template>
          </wd-card>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { getProductNewsAPI, type ProductNewsVo } from '@/service/index/news'
import { useUserStore } from '@/store'

defineOptions({
  name: 'Home',
})

const userStore = useUserStore()
const author = ref('响应式数据')
const productNews = ref<ProductNewsVo[]>([
  {
    id: '1',
    title: '2025年立个 flag,迭代十次大的产品功能更新',
    desc: '图床能力升级了，增加了对兼容 S3 协议的所有图床的支持。 例如七牛云、雨云、缤纷云、MinIO 等常见图床品牌。',
    link: 'https://mp.weixin.qq.com/s/dBpa-9DRj0zQ5wJSJSL7XQ',
    cover: '',
  },
])

// 卡片滑动相关状态
// 当前激活的卡片索引
const activeCardIndex = ref(0)
// 是否显示滑动提示
const showScrollHint = ref(true)
// 控制右侧渐变遮罩的显示
const showScrollMask = ref(true)

// 计算是否显示登录状态提示
const showLoginHint = computed(() => {
  return userStore.isLoginLoading && !userStore.isLogined
})

// 处理滑动事件
const handleScroll = (e: any) => {
  const { scrollLeft, scrollWidth, width } = e.detail

  // 计算当前激活的卡片索引
  // 假设每个卡片宽度为240rpx，加上间距
  const cardWidth = 240 + 32 // 卡片宽度 + 间距
  const index = Math.round(scrollLeft / cardWidth)
  activeCardIndex.value = Math.min(Math.max(index, 0), 2) // 限制在 0-2 之间

  // 更精确地判断是否滑动到最右边
  // 当滑动到最右边时，完全隐藏遮罩
  if (scrollLeft + width >= scrollWidth - 10) {
    showScrollHint.value = false
    showScrollMask.value = false // 隐藏遮罩
  } else {
    showScrollMask.value = true // 显示遮罩

    // 如果回到最左边，显示滑动提示
    if (scrollLeft === 0) {
      showScrollHint.value = true
    }
  }
}

// 获取产品新闻
const fetchProductNews = async () => {
  try {
    const { data: res } = await getProductNewsAPI()
    console.log('获取产品新闻成功:', res)
    if (res && res.length > 0) {
      // 对新闻按id倒序排序
      const sortedNews = [...res].sort((a, b) => {
        // 确保id是数字进行比较，如果不是则转换
        const idA = parseInt(a.id) || 0
        const idB = parseInt(b.id) || 0
        return idB - idA // 倒序排序
      })

      // 最多取两条新闻
      productNews.value = sortedNews.slice(0, 2)
      console.log('处理后的产品新闻:', productNews.value)
    }
  } catch (error) {
    console.error('获取产品新闻失败:', error)
  }
}

// 打开链接
const openPage = (url: string) => {
  console.log('打开链接:', url)
  uni.navigateTo({
    url: '/pages/index/news/news?link=' + url,
  })
}

// 测试 uni API 自动引入
onLoad(() => {
  console.log(author)
  // 设置分享按钮及相关属性
  wx.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline'],
  })
  // 获取产品新闻
  fetchProductNews()
})

// 页面加载完成后初始化滑动状态
onMounted(() => {
  // 默认选中第一个卡片
  activeCardIndex.value = 0
  // 显示滑动提示
  showScrollHint.value = true
  // 显示滑动遮罩
  showScrollMask.value = true
})

onShareAppMessage(() => {
  return {
    title: '目前最好用的Notion移动剪藏工具之一，也许没有之一',
    path: '/pages/index/index',
  }
})

// 打开下厨房文档
const cookbook = () => {
  uni.openEmbeddedMiniProgram({
    appId: 'wxd45c635d754dbf59',
    path: 'pages/detail/detail?url=https%3A%2F%2Fdocs.qq.com%2Faio%2FDZldYc05MQmxoa3FZ',
  })
}

// 打开常见问题文档
const hotQuestion = () => {
  uni.openEmbeddedMiniProgram({
    appId: 'wxd45c635d754dbf59',
    path: 'pages/detail/detail?url=https%3A%2F%2Fdocs.qq.com%2Faio%2FDZnBuek5yS2N2cFhv',
  })
}

// 打开信件文档
const letter = () => {
  uni.openEmbeddedMiniProgram({
    appId: 'wxd45c635d754dbf59',
    path: 'pages/detail/detail?url=https%3A%2F%2Fdocs.qq.com%2Faio%2FDZkZXQ25pRGVjbWxP',
  })
}

// 跳转到快捷剪藏页面
const toQuickClip = () => {
  uni.navigateTo({
    url: '/pages/index/quickclip/quickclip',
  })
}
</script>

<style lang="scss" scoped>
.tr-tag {
  padding: 6rpx 12rpx;
  font-size: 10px;
  border-radius: 6rpx;
}

:deep(.cut-button) {
  width: 690rpx !important;
  height: 90rpx !important;
  margin-top: 16px !important;
  margin-bottom: 20px !important;
  font-size: 16px !important;
  line-height: 24px !important;
  border: 0.1rpx solid #337ea9 !important;
  border-radius: 12px !important;
  box-shadow: #edeef1 0px 1px 7px 0px;
}
/* 滚动视图样式 */
.card-scroll-view {
  width: 100%;
  // padding: 5rpx 0;
  white-space: nowrap;
}
/* 右侧渐变遮罩 */
.scroll-fade-mask {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  width: 40rpx;
  height: 100%;
  pointer-events: none; /* 允许点击穿透 */
  background: linear-gradient(
    to right,
    rgba(248, 248, 247, 0) 0%,
    rgba(248, 248, 247, 0.5) 40%,
    rgba(248, 248, 247, 0.8) 70%,
    rgba(248, 248, 247, 0.98) 100%
  );
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease; /* 添加过渡效果 */
}

.card-container {
  display: inline-flex;
  padding: 15rpx 0;
  padding-right: 60rpx; /* 增加右侧边距，确保最后一个卡片完全显示出来 */
  padding-left: 10rpx;
}
/* 卡片样式 */
.card-item {
  display: inline-flex;
  flex-shrink: 0;
  width: 240rpx;
  height: 220rpx;
  margin-right: 32rpx;
  overflow: hidden;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.card-hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-6rpx) scale(1.02);
}

.card-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border: 1px solid #ebeae7;
  border-radius: 10px;
  /* 添加与card-item相同的圆角 */
}

.card-icon-area {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 60%;
}

.card-text-area {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  height: 40%;
  // padding: 0 16rpx;
}

.card-title {
  margin-top: 20rpx;
  margin-left: 32rpx;
  font-size: 12px;
  font-weight: 400;
  line-height: 40rpx;
  color: #999999;
  text-align: center;
}

// 滑动指示器样式
.scroll-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 12rpx;
}

.indicator-dot {
  width: 16rpx;
  height: 16rpx;
  margin: 0 8rpx;
  background-color: #e0e0e0;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.indicator-dot.active {
  width: 24rpx;
  background-color: #337ea9;
}
/* 滑动提示样式 */
.scroll-hint {
  position: absolute;
  top: 50%;
  right: 10rpx;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-50%);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }

  50% {
    opacity: 0.8;
    transform: translateY(-50%) scale(1.1);
  }

  100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}

.card-item:last-child {
  margin-right: 0;
}
.news-container {
  padding: 10rpx 0;
}

.card-item:last-child {
  margin-right: 0;
}

.login-hint-bar {
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 12rpx 0;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.login-hint-content {
  display: flex;
  gap: 16rpx;
  align-items: center;
  justify-content: center;
}

.login-hint-spinner {
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.login-hint-text {
  font-size: 24rpx;
  color: white;
  opacity: 0.9;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
