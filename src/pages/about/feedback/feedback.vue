<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '提供反馈',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="feedback-container">
    <wd-form :model="formData" label-align="top">
      <!-- 反馈类型 -->
      <wd-card class="mb-4 shadow rounded-lg">
        <view class="card-title">反馈类型</view>
        <view class="form-item-container">
          <wd-radio-group v-model="formData.type" class="feedback-type-radio">
            <wd-radio
              v-for="item in feedbackTypes"
              :key="item.value"
              :value="item.value"
              custom-class="feedback-radio-item"
            >
              {{ item.label }}
            </wd-radio>
          </wd-radio-group>
        </view>
      </wd-card>

      <!-- 反馈内容 -->
      <wd-card class="mb-4 shadow rounded-lg">
        <view class="card-title">反馈内容</view>
        <view class="form-item-container">
          <wd-textarea
            v-model="formData.content"
            placeholder="请详细描述您的建议或遇到的问题"
            :maxlength="500"
            show-word-limit
            autosize
            :rows="6"
          ></wd-textarea>
          <view class="form-tips">
            <text>内容不能超过500字</text>
          </view>
        </view>
      </wd-card>

      <!-- 提交按钮 -->
      <view class="form-footer">
        <wd-button type="primary" block @click="handleSubmit" class="shadow-sm">提交反馈</wd-button>
      </view>
    </wd-form>
  </view>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import { useToast } from 'wot-design-uni'
import { submitFeedback, type FeedbackBody } from '@/service/feedback/feedback'
import { extractErrorMessage } from '@/utils'

const toast = useToast()

// 表单数据
const formData = reactive({
  content: '',
  type: '0', // 使用字符串类型以匹配 FeedbackBody 接口
})

// 反馈类型选项
const feedbackTypes = [
  { value: '0', label: '功能建议' },
  { value: '1', label: '问题反馈' },
  { value: '2', label: '其他' },
]

// 提交反馈
const handleSubmit = async () => {
  // 验证表单
  if (!formData.content.trim()) {
    toast.warning('请输入反馈内容')
    return
  }

  // 验证字数限制
  if (formData.content.length > 500) {
    toast.warning('反馈内容不能超过500字')
    return
  }

  try {
    toast.loading('提交中...')

    // 准备请求数据
    const feedbackData: FeedbackBody = {
      content: formData.content,
      type: formData.type,
    }

    // 调用提交接口
    const { data } = await submitFeedback(feedbackData)

    if (data) {
      toast.success('感谢您的反馈，我们会认真考虑您的建议')
      // 重置表单
      formData.content = ''
      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      toast.error('提交失败，请重试')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    console.error('提交反馈失败:', errorMessage)
    toast.error('提交失败: ' + (errorMessage || '未知错误'))
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}
</script>

<style lang="scss" scoped>
.feedback-container {
  min-height: 100vh;
  padding: 16px 0rpx;
  background-color: #f7f8fa;
}

.card-title {
  margin-bottom: 24rpx;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.form-item-container {
  margin-bottom: 16rpx;
}

.form-tips {
  margin-top: 12rpx;
  font-size: 12px;
  color: #999;
}

.form-footer {
  padding: 0 20rpx;
  margin-top: 60rpx;
}

:deep(.wd-button) {
  height: 88rpx;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.wd-button--primary) {
  background-color: #337ea9;
  border-color: #337ea9;
}

:deep(.wd-button--primary:active) {
  background-color: #2a6a8f;
  border-color: #2a6a8f;
  transform: translateY(2rpx);
}
/* 自定义单选按钮样式 */
:deep(.feedback-type-radio) {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

:deep(.feedback-radio-item) {
  padding: 20rpx 24rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  transition: all 0.2s ease;
}

:deep(.feedback-radio-item.is-checked) {
  background-color: rgba(51, 126, 169, 0.1);
  border-left: 8rpx solid #337ea9;
}

:deep(.wd-radio__label) {
  font-size: 16px;
  color: #333;
}

:deep(.wd-radio__shape) {
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid #999;
}

:deep(.wd-radio__shape:after) {
  width: 14rpx;
  height: 14rpx;
  background-color: #337ea9;
}

:deep(.is-checked .wd-radio__shape) {
  border-color: #337ea9;
}

:deep(.wd-textarea) {
  padding: 12rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

:deep(.wd-input__body) {
  padding: 0 12rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

:deep(.wd-card) {
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
</style>
