<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '实验室',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="labor-container">
    <!-- 顶部图片区域 -->
    <view class="lab-image-container">
      <view class="lab-image-wrapper">
        <view class="i-carbon-chemistry w-180rpx h-180rpx c-#337ea9"></view>
      </view>
    </view>

    <!-- 标题和说明 -->
    <view class="lab-content">
      <wd-text text="实验室" size="22px" bold custom-class="!c-#337ea9 mb-4"></wd-text>
      <wd-text
        text="我正在开发一些令人兴奋的新功能！"
        size="16px"
        custom-class="!c-#333 mb-3 text-center"
      ></wd-text>
      <wd-text
        text="这些功能目前处于测试阶段，很快就会与您见面。"
        size="14px"
        custom-class="!c-#666 mb-6 text-center"
      ></wd-text>
    </view>

    <!-- 功能预告卡片 -->
    <wd-card custom-class="feature-card tr-shadow mb-4">
      <view class="card-title flex items-center">
        <view class="i-carbon-idea w-20px h-20px mr-2 c-#337ea9"></view>
        <text>即将推出</text>
      </view>
      <view class="feature-list">
        <view class="feature-item">
          <view class="i-solar:stars-line-line-duotone w-16px h-16px c-#67c23a mr-2"></view>
          <wd-text text="先来一打小更新" size="14px" custom-class="!c-#333"></wd-text>
        </view>
        <view class="feature-item">
          <view class="i-solar:stars-line-line-duotone w-16px h-16px c-#67c23a mr-2"></view>
          <wd-text text="AI助手" size="14px" custom-class="!c-#333"></wd-text>
        </view>
        <!-- <view class="feature-item">
          <view class="i-solar:stars-line-line-duotone w-16px h-16px c-#67c23a mr-2"></view>
          <wd-text text="AI" size="14px" custom-class="!c-#333"></wd-text>
        </view> -->
      </view>
    </wd-card>

    <!-- 反馈按钮 -->
    <view class="feedback-container">
      <wd-button type="info" custom-class="feedback-btn" @click="handleFeedback">
        提供建议
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
// 实验室页面逻辑

// 打开反馈页面
const handleFeedback = () => {
  uni.navigateTo({
    url: '/pages/about/feedback/feedback',
  })
}
</script>

<style lang="scss" scoped>
.labor-container {
  min-height: 100vh;
  padding: 32rpx;
  background-color: #f7f8fa;
}

.lab-image-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.lab-image-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240rpx;
  height: 240rpx;
  background-color: rgba(51, 126, 169, 0.1);
  border-radius: 50%;
}

.lab-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.feature-card,
.subscribe-card {
  overflow: hidden;
  border-radius: 16rpx;
}

.card-title {
  margin-bottom: 24rpx;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.wechat-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 20rpx 0;
}

.wechat-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
  background-color: rgba(7, 193, 96, 0.1);
  border-radius: 50%;
}

.wechat-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.feedback-container {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}

.feedback-btn {
  width: 100%;
  height: 88rpx;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  letter-spacing: 1px;
  background-color: #f5f7fa;
  border-color: #ddd;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.wd-button--primary:active) {
  background-color: #2a6a8f;
  border-color: #2a6a8f;
  transform: translateY(2rpx);
}

:deep(.wd-button--info:active) {
  color: #555;
  background-color: #e8e8e8;
  border-color: #ccc;
  transform: translateY(2rpx);
}

:deep(.wd-card) {
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
</style>
