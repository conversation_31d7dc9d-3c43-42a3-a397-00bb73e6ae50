<!-- eslint-disable import/no-duplicates -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view
    class="overflow-hidden flex flex-col items-center justify-start w-full h-100% m-0 bg-#f8f8f7 pb-safe"
  >
    <!-- 上层个人信息面板 -->
    <view
      class="bg-white w-690rpx mt-12px flex flex-col items-center justify-center tr-shadow rounded-12px"
    >
      <!-- 使用封装的用户信息面板组件 -->
      <user-profile :user-info="userInfo" class="w-full" />
      <!-- 数据面板 -->
      <stats-panel :stats-items="statsItems" class="w-full mb-4" />
    </view>
    <!-- 间距 -->
    <wd-gap height="12px"></wd-gap>
    <!-- 下层功能面板 -->
    <view class="rounded-12px bg-white w-690rpx mx-3 py-2">
      <wd-cell-group border custom-class="mx-0">
        <wd-cell
          title="个人信息"
          custom-title-class="text-size-16px line-height-28px c-#333"
          center
          is-link
          to="/pages/about/personal/personal"
        >
          <template #icon>
            <view class="w-28px h-28px mr-12px line-height-28px center rounded-1">
              <view class="i-solar:settings-linear w-21px h-21px c-#696969"></view>
            </view>
          </template>
        </wd-cell>
        <wd-cell
          title="图床配置"
          custom-title-class="text-size-16px line-height-28px c-#333"
          center
          is-link
          to="/pages/about/cloudpic/cloudpic"
        >
          <template #icon>
            <view class="w-28px h-28px mr-12px line-height-28px center rounded-1">
              <view class="i-solar:folder-cloud-linear w-21px h-21px c-#696969"></view>
            </view>
          </template>
        </wd-cell>
        <wd-cell
          title="个性设置"
          custom-title-class="text-size-16px line-height-28px c-#333"
          center
          is-link
          to="/pages/about/profile/profile"
        >
          <template #icon>
            <view class="w-28px h-28px mr-12px line-height-28px center rounded-1">
              <view class="i-solar:magic-stick-3-linear w-21px h-21px c-#696969"></view>
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>
    <wd-gap height="12px"></wd-gap>

    <view class="rounded-12px bg-white w-690rpx mx-3 py-2">
      <wd-cell-group border custom-class="mx-0">
        <wd-cell
          title="会员计划"
          custom-title-class="text-size-16px line-height-28px c-#333"
          center
          is-link
          to="/pages/about/member/member"
        >
          <template #icon>
            <view class="w-28px h-28px mr-12px line-height-28px center rounded-1">
              <view class="i-solar:crown-line-linear w-21px h-21px c-#696969"></view>
            </view>
          </template>
        </wd-cell>
        <wd-cell
          title="收藏助手"
          custom-title-class="text-size-16px line-height-28px c-#333"
          center
          is-link
          to="/pages/about/assistant/assistant"
        >
          <template #icon>
            <view class="w-28px h-28px mr-12px line-height-28px center rounded-1">
              <view class="i-fluent:bot-add-32-regular w-21px h-21px c-#696969"></view>
            </view>
          </template>
        </wd-cell>
        <wd-cell
          title="Obsidian"
          custom-title-class="text-size-16px line-height-28px c-#333"
          center
          is-link
          to="/pages/about/obsidian/obsidian"
        >
          <template #icon>
            <view class="w-28px h-28px mr-12px line-height-28px center rounded-1">
              <image class="w-21px h-21px" src="/static/images/about/ob.svg" mode="aspectFit" />
            </view>
          </template>
        </wd-cell>
        <wd-cell
          title="实验室"
          custom-title-class="text-size-16px line-height-28px c-#333"
          center
          is-link
          to="/pages/about/labor/labor"
        >
          <template #icon>
            <view class="w-28px h-28px mr-12px line-height-28px center rounded-1">
              <view class="i-solar:telescope-outline w-21px h-21px c-#696969"></view>
            </view>
          </template>
        </wd-cell>
        <wd-cell
          title="我的积分"
          custom-title-class="text-size-16px line-height-28px c-#333"
          center
          is-link
          to="/pages/about/points/points"
        >
          <template #icon>
            <view class="bg-white w-28px h-28px mr-12px line-height-28px center rounded-1">
              <view class="i-solar:stars-minimalistic-line-duotone w-21px h-21px c-#696969"></view>
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>
    <wd-gap height="12px"></wd-gap>
    <view class="rounded-12px bg-white w-690rpx mx-3 py-2">
      <wd-cell-group border custom-class="mx-0">
        <wd-cell
          title="问题反馈"
          custom-title-class="text-size-16px line-height-28px c-#333"
          center
          is-link
          to="/pages/about/feedback/feedback"
        >
          <template #icon>
            <view class="w-28px h-28px mr-12px line-height-28px center rounded-1">
              <view class="i-solar:bug-linear w-21px h-21px c-#696969"></view>
            </view>
          </template>
        </wd-cell>
        <wd-cell
          title="五星好评"
          custom-title-class="text-size-16px line-height-28px c-#333"
          center
          is-link
          @click="good"
        >
          <template #icon>
            <view class="w-28px h-28px mr-12px line-height-28px center rounded-1">
              <view class="i-solar:like-linear w-21px h-21px c-#696969"></view>
            </view>
          </template>
        </wd-cell>
        <wd-cell
          title="隐私声明"
          custom-title-class="text-size-16px line-height-28px c-#333"
          center
          is-link
          to="/pages/about/privacy/privacy"
        >
          <template #icon>
            <view class="w-28px h-28px mr-12px line-height-28px center rounded-1">
              <view
                class="i-solar:shield-keyhole-minimalistic-linear w-21px h-21px c-#696969"
              ></view>
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </view>
    <wd-gap height="16px"></wd-gap>
  </view>
</template>

<script lang="ts" setup>
import UserProfile from './components/user-profile.vue'
import StatsPanel from './components/stats-panel.vue'
// 导入 toast 用于显示提示信息
import { useToast } from 'wot-design-uni'
import { useUserStore } from '@/store/user'
import { computed, ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { DEFAULT_AVATAR } from '@/constants/common'
import { extractErrorMessage } from '@/utils'

import { getPromotionData } from '@/service/points/points'
import { getUserInfo } from '@/service/user/user'

// 使用 userStore 获取用户信息
const userStore = useUserStore()

// 从 userStore 中获取用户信息
const userInfo = computed(() => ({
  avatar:
    userStore.userInfo.avatar ||
    'https://res.cloudinary.com/dwal1nlws/image/upload/v1740285825/wexin/mp/461865305a33adc8d4dbeae75ed08502.png',
  nickname: userStore.userInfo.nickname || '游客用户',
  createTime: userStore.userInfo.createTime
    ? `注册时间: ${userStore.userInfo.createTime}`
    : '注册时间: --',
  endTime: userStore.userInfo.endTime
    ? `会员到期时间: ${userStore.userInfo.endTime}`
    : '会员到期时间: --',
  userType: userStore.userInfo.userType || '普通用户',
  memberNo: userStore.userInfo.memberNo || 0,
}))

// 积分数据 - 从缓存初始化
const pointsSum = ref(userStore.getCachedPointsData().sum || 0)
const pointsAvailable = ref(userStore.getCachedPointsData().availableSum || 0)
const shareCount = ref(userStore.getCachedPointsData().shareCount || 0)

// 标记是否已经在 onLoad 中加载过数据
const hasLoadedData = ref(false)

// 数据统计项
const statsItems = computed(() => [
  { value: pointsSum.value || '0', label: '总积分', valueColor: '#337ea9' },
  { value: pointsAvailable.value || '0', label: '可用积分', valueColor: '#337ea9' },
  { value: shareCount.value || '0', label: '分享人次', valueColor: '#337ea9' },
])

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    console.log('开始获取用户详细信息')
    const { data } = await getUserInfo()

    if (data) {
      // 合并新获取的用户信息与现有信息
      const mergedUserInfo = { ...userStore.userInfo, ...data }
      if (!mergedUserInfo.avatar || mergedUserInfo.avatar.startsWith('wxfile')) {
        mergedUserInfo.avatar = DEFAULT_AVATAR
      }
      userStore.setUserInfo(mergedUserInfo)
      // 更新用户信息刷新时间
      userStore.updateUserInfoRefreshTime()
      // 重置刷新标记
      userStore.setNeedRefreshUserInfo(false)
      console.log('用户详细信息已更新:', mergedUserInfo)
    } else {
      console.warn('获取用户详细信息失败: 返回数据为空')
    }
  } catch (error) {
    console.error('获取用户详细信息失败:', error)
    const errorMessage = extractErrorMessage(error)
    console.error('错误详情:', errorMessage)
  }
}

// 获取积分数据 - 只在页面首次加载时调用
const fetchPointsData = async () => {
  try {
    console.log('获取积分数据')

    const res = await getPromotionData()
    if (res.data) {
      // 更新本地数据
      pointsSum.value = res.data.sum
      pointsAvailable.value = res.data.availableSum
      shareCount.value = res.data.shareCount

      // 更新缓存数据
      userStore.updateCachedPointsData({
        sum: res.data.sum,
        availableSum: res.data.availableSum,
        shareCount: res.data.shareCount,
      })

      console.log('积分数据已更新')
    } else {
      console.warn('获取积分数据失败: 返回数据为空')
    }
  } catch (error) {
    console.error('获取积分数据失败', error)
    const errorMessage = extractErrorMessage(error)
    console.error('错误详情:', errorMessage)
  }
}
// 页面首次加载时获取数据
onLoad(() => {
  console.log('onLoad: 首次加载页面')
  // 标记已加载数据
  hasLoadedData.value = true

  // 无条件获取积分数据 - 积分数据只在首次加载时获取
  console.log('onLoad: 获取积分数据')
  fetchPointsData()
})

// 页面显示时根据条件获取数据
onShow(() => {
  console.log('onShow: 页面显示')

  // 如果是首次加载，onLoad 已经处理过数据，不需要重复处理
  if (!hasLoadedData.value) {
    // 判断是否需要刷新用户信息
    // 只有在以下情况才刷新：
    // 1. 数据已过期
    // 2. 明确设置了需要刷新的标记
    if (userStore.isUserInfoStale || userStore.needRefreshUserInfo) {
      console.log('onShow: 需要刷新用户信息')
      fetchUserInfo()
    } else {
      console.log('onShow: 不需要刷新用户信息，使用本地存储的数据')
    }

    // 注意：积分数据只在 onLoad 时加载，不需要在 onShow 时重新加载
    // 因为没有操作会影响积分数据
  } else {
    // 重置标记，下次 onShow 时正常检查
    hasLoadedData.value = false
  }
})

// 初始化 toast
const toast = useToast()

// 五星好评点击事件
const good = () => {
  toast.show({
    iconClass: 'heart-filled',
    msg: '谢谢！',
  })
}
</script>

<style lang="scss" scoped>
.test-css {
  // mt-4=>1rem=>16px;
  margin-top: 16px;
}

:deep(.top-safe-gap) {
  border-radius: 8px 8px 0 0 !important;
  box-shadow: rgb(237, 238, 241) 0px 1px 7px 0px;
}

:deep(.bottom-safe-gap) {
  border-radius: 0 0 8px 8px !important;
  box-shadow: rgb(237, 238, 241) 0px 1px 7px 0px;
}
/* 已删除未使用的图标CSS类 */
</style>
