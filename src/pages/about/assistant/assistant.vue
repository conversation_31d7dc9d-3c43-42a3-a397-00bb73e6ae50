<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '收藏助手',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="overflow-hidden bg-#F3F3F3 min-h-screen flex flex-col items-center px-4">
    <!-- 加载状态 -->
    <wd-loading v-if="loading" color="#8066FF" custom-class="mt-[200rpx]" />

    <view v-else class="w-full">
      <view class="w-full" v-if="isMember && hasBindCpUser">
        <!-- 已绑定状态 -->
        <wd-gap height="32px"></wd-gap>
        <wd-text
          size="20px"
          text="您已绑定收藏助手"
          color="#3a3a3a"
          bold
          custom-class="text-center block"
        />

        <!-- 功能介绍卡片 -->
        <wd-gap height="24px"></wd-gap>
        <view class="mx-[20rpx] mb-[32rpx] bg-white rounded-[16rpx] tr-shadow">
          <view class="p-[32rpx]">
            <wd-text size="16px" text="收藏助手可以做什么？" color="#3a3a3a" bold />
            <wd-gap height="16px"></wd-gap>
            <wd-text
              size="14px"
              text="1、支持文字、图片、链接"
              color="#333"
              custom-class="mb-3 block"
            />
            <wd-text
              size="14px"
              text="2、支持文字末尾通过#号加标签"
              color="#333"
              custom-class="mb-3 block"
            />
            <wd-text size="14px" text="3、支持外部平台内容直接转发" color="#333" />
          </view>
        </view>

        <!-- 增强功能卡片 -->
        <view class="mx-[20rpx] mb-[32rpx] bg-white rounded-[16rpx] tr-shadow">
          <view class="p-[32rpx]">
            <wd-text size="16px" text="如您需要以下能力,请开启增强开关" color="#3a3a3a" bold />
            <wd-gap height="16px"></wd-gap>
            <wd-text size="14px" text="1、支持保存文件" color="#333" custom-class="mb-3 block" />
            <wd-text
              size="14px"
              text="2、支持保存聊天记录内容"
              color="#333"
              custom-class="mb-3 block"
            />
            <wd-text size="14px" text="3、支持保存语音、视频等内容" color="#333" />
          </view>
        </view>

        <!-- 增强功能开关 -->
        <view class="p-[32rpx] flex justify-between items-center">
          <view class="flex items-center">
            <wd-text size="16px" text="开启增强功能" color="#3a3a3a" bold />
            <view class="i-solar:info-circle-linear w-[16px] h-[16px] c-#999 ml-[8rpx]"></view>
          </view>
          <wd-switch v-model="enhanceEnabled" @change="toggleEnhance" size="20px" />
        </view>
      </view>

      <!-- 非会员提示区域 -->
      <view class="w-full flex flex-col items-center mt-[40rpx]" v-else-if="!isMember">
        <!-- 顶部图标和标题 -->
        <wd-img src="/static/assit.jpg" width="180rpx" height="180rpx" custom-class="mb-[24rpx]" />
        <wd-text size="20px" text="会员功能" color="#333" bold custom-class="mb-[16rpx]" />
        <wd-text
          size="15px"
          text="收藏助手功能仅向会员用户开放"
          color="#666"
          custom-class="mb-[40rpx]"
        />

        <!-- 功能介绍卡片 -->
        <view class="mx-[20rpx] mb-[32rpx] bg-white rounded-[16rpx] tr-shadow w-[90%]">
          <view class="p-[32rpx]">
            <view class="flex items-center mb-[16rpx]">
              <view class="i-solar:magic-stick-linear w-[24px] h-[24px] c-#337ea9 mr-[8rpx]"></view>
              <wd-text size="18px" text="收藏助手能做什么？" color="#333" bold />
            </view>
            <wd-gap height="16px"></wd-gap>

            <view class="feature-item">
              <view class="feature-icon i-solar:notes-linear"></view>
              <view class="feature-content">
                <wd-text
                  size="14px"
                  text="多平台内容一键转发即可完成收藏"
                  color="#333"
                  bold
                  custom-class="mb-[8rpx] block"
                />
                <wd-text
                  size="12px"
                  text="支持外部平台内容直接转发，自动同步保存到 Notion"
                  color="#666"
                />
              </view>
            </view>

            <view class="feature-item">
              <view class="feature-icon i-solar:hashtag-outline"></view>
              <view class="feature-content">
                <wd-text
                  size="14px"
                  text="收藏助手智能识别添加标签"
                  color="#333"
                  bold
                  custom-class="mb-[8rpx] block"
                />
                <wd-text
                  size="12px"
                  text="转发时，在输入框中添加 # 标签，即可自动识别并添加标签，可以添加多个"
                  color="#666"
                />
              </view>
            </view>

            <view class="feature-item">
              <view class="feature-icon i-solar:cloud-upload-outline"></view>
              <view class="feature-content">
                <wd-text
                  size="14px"
                  text="转发保存聊天记录、文件、视频等内容"
                  color="#333"
                  bold
                  custom-class="mb-[8rpx] block"
                />
                <wd-text
                  size="12px"
                  text="支持文字、图片、链接、文件、聊天记录、视频、语音等内容(<=10Mb)"
                  color="#666"
                />
              </view>
            </view>
          </view>
        </view>

        <!-- 升级会员按钮 -->
        <view class="w-[90%] mb-[40rpx]">
          <wd-button type="primary" block custom-class="upgrade-button" @click="toMemberPage">
            <view class="flex items-center justify-center">
              <view class="i-solar:crown-bold w-[20px] h-[20px] mr-[8rpx]"></view>
              <text>升级会员解锁收藏助手</text>
            </view>
          </wd-button>
        </view>
      </view>

      <view v-else-if="isMember" class="w-full">
        <!-- 图床配置检查 -->
        <view class="w-full mt-[40rpx] flex justify-center" v-if="!hasCloudinaryConfig">
          <wd-card custom-class="tr-shadow rounded-[24rpx] overflow-hidden !w-[690rpx] !m-0">
            <view class="px-[0rpx] py-[48rpx]">
              <!-- 头部标题区域 -->
              <view class="flex items-center justify-center mb-[32rpx]">
                <view class="flex items-center">
                  <view
                    class="w-[32px] h-[32px] bg-gradient-to-r from-[#337ea9] to-[#4a90e2] rounded-[8px] flex items-center justify-center mr-[12rpx]"
                  >
                    <view class="i-solar:gallery-add-broken w-[20px] h-[20px] c-white"></view>
                  </view>
                  <wd-text size="20px" text="图床配置" color="#3a3a3a" bold />
                </view>
              </view>

              <!-- 图标区域 -->
              <view class="flex items-center justify-center mb-[40rpx]">
                <view
                  class="w-[160rpx] h-[160rpx] bg-gradient-to-br from-[#f8fafc] to-[#e2e8f0] rounded-full flex items-center justify-center shadow-sm"
                >
                  <view class="i-solar:cloud-upload-broken w-[80rpx] h-[80rpx] c-#337ea9"></view>
                </view>
              </view>

              <!-- 文字说明区域 -->
              <view class="text-center mb-[40rpx]">
                <wd-text
                  size="18px"
                  text="您还未配置图床！"
                  color="#3a3a3a"
                  bold
                  custom-class="mb-[16rpx] block"
                />
                <wd-text
                  size="14px"
                  text="开启收藏助手功能需要您先配置好图床"
                  color="#666"
                  custom-class="leading-relaxed"
                />
              </view>

              <!-- 操作按钮 -->
              <wd-button
                type="primary"
                block
                custom-class="tr-shadow !h-[88rpx] !rounded-[16rpx]"
                @click="toCloudinary"
              >
                <view class="flex items-center justify-center">
                  <view
                    class="i-solar:settings-minimalistic-broken w-[20px] h-[20px] mr-[8rpx]"
                  ></view>
                  <text class="text-[16px] font-medium">立即配置</text>
                </view>
              </wd-button>
            </view>
          </wd-card>
        </view>

        <!-- 绑定流程 -->
        <view v-else class="w-full">
          <wd-gap height="32px"></wd-gap>
          <!-- 步骤条 -->
          <wd-steps :active="activeStep" align-center>
            <wd-step title="添加好友" description="添加收藏助手为好友" />
            <wd-step title="绑定账号" description="生成并发送绑定口令" />
            <wd-step title="完成验证" description="发送消息验证同步" />
          </wd-steps>
          <wd-gap height="32px"></wd-gap>
          <view>
            <!-- 第一步 -->
            <view v-show="activeStep === 0" class="step-content">
              <wd-text
                size="14px"
                text="添加收藏助手好友,添加后注意不要点拒绝内容存档,否则助手无法正常工作"
                type="primary"
              />
              <wd-gap height="16px"></wd-gap>
              <view class="w-[400rpx] h-[400rpx] mx-auto">
                <wd-img
                  src="https://notionclipper.fun/images/friend.jpg"
                  width="400rpx"
                  height="400rpx"
                  custom-class="rounded-[20rpx]"
                  :cache="true"
                  :enable-preview="true"
                />
              </view>
              <wd-gap height="32px"></wd-gap>
              <view class="flex justify-center">
                <wd-button type="primary" size="medium" @click="nextStep">下一步</wd-button>
              </view>
            </view>

            <!-- 第二步 -->
            <view v-show="activeStep === 1" class="step-content">
              <wd-text
                size="14px"
                text="生成并复制口令,发送给收藏助手进行账号绑定"
                type="primary"
              />
              <wd-gap height="16px"></wd-gap>
              <view class="flex flex-col items-center">
                <wd-button type="primary" size="medium" @click="generateSecret">
                  生成绑定口令
                </wd-button>
                <view v-if="secret.length > 0" class="mt-[24rpx] w-full">
                  <view class="bg-[#f7f8fa] p-[24rpx] rounded-[16rpx]">
                    <wd-text size="14px" :text="secret" color="#333" custom-class="secret-text" />
                  </view>
                  <view class="flex justify-end mt-[16rpx]">
                    <view class="copy-btn flex items-center" @click="copySecret">
                      <view
                        class="i-solar:copy-linear w-[16px] h-[16px] mr-[4rpx] c-#8066FF"
                      ></view>
                      <wd-text
                        size="14px"
                        text="复制口令"
                        color="#8066FF"
                        custom-class="cursor-pointer"
                      />
                    </view>
                  </view>
                </view>
              </view>
              <wd-gap height="32px"></wd-gap>
              <view class="flex justify-center gap-4">
                <wd-button plain size="medium" @click="prevStep">上一步</wd-button>
                <wd-button type="primary" size="medium" @click="nextStep">下一步</wd-button>
              </view>
            </view>

            <!-- 第三步 -->
            <view v-show="activeStep === 2" class="step-content">
              <wd-text size="14px" text="1、随便发送一段文字" type="primary" />
              <wd-gap height="16px"></wd-gap>
              <wd-text size="14px" text="2、收藏助手是不会回复消息的" type="primary" />
              <wd-gap height="16px"></wd-gap>
              <wd-text size="14px" text="3、等待片刻,检查消息数据库中是否同步成功" type="primary" />
              <wd-gap height="32px"></wd-gap>
              <view class="flex justify-center">
                <wd-button plain size="medium" @click="prevStep">上一步</wd-button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useToast, useMessage } from 'wot-design-uni'
import { onShow } from '@dcloudio/uni-app'
import { useUserStore } from '@/store'
import {
  getHasBindCpWxUser,
  getBindCpWxUserSecret,
  getEnhanceSwitch,
  enhanceSwitch,
} from '@/service/robot/robot'
import { getHasBindCloudinary } from '@/service/piccloud/cloudinary'
import { getS3Config } from '@/service/piccloud/s3'
import { extractErrorMessage } from '@/utils'

const toast = useToast()
const message = useMessage()
const userStore = useUserStore()

// 状态变量
const hasBindCpUser = ref(false)
const hasCloudinaryConfig = ref(false)
const secret = ref('')
const activeStep = ref(0)
const loading = ref(false)
const enhanceEnabled = ref(false) // 增强功能开关状态

// 从 userStore 获取用户信息
const userInfo = computed(() => userStore.userInfo)

// 判断用户是否是会员
const isMember = computed(() => {
  const userType = userInfo.value.userType || ''
  const mainUserType = userInfo.value.mainUserVo?.userType || ''
  return (
    userType === '年卡会员' ||
    userType === '永久会员' ||
    mainUserType === '年卡会员' ||
    mainUserType === '永久会员'
  )
})

// 页面初始化
onShow(() => {
  initPageData()
})

// 初始化页面数据
const initPageData = async () => {
  try {
    loading.value = true

    // 并行执行三个请求，提高页面加载速度
    await Promise.all([
      checkBindStatus().catch((error) => {
        console.error('检查绑定状态失败:', error)
        // 单个请求失败不影响整体流程
        hasBindCpUser.value = false
      }),

      checkCloudinaryConfig().catch((error) => {
        console.error('检查图床配置失败:', error)
        // 单个请求失败不影响整体流程
        hasCloudinaryConfig.value = false
      }),

      checkEnhanceStatus().catch((error) => {
        console.error('检查增强功能状态失败:', error)
        // 单个请求失败不影响整体流程
        enhanceEnabled.value = false
      }),
    ])
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    toast.error(errorMessage || '加载数据失败，请重试')
  } finally {
    loading.value = false
  }
}

// 检查绑定状态
const checkBindStatus = async () => {
  try {
    const res = await getHasBindCpWxUser()
    if (res.success) {
      hasBindCpUser.value = res.data
    } else {
      toast.error(res.msg || '获取绑定状态失败')
    }
  } catch (error) {
    console.error('检查绑定状态失败:', error)
    hasBindCpUser.value = false
  }
}

// 检查图床配置
const checkCloudinaryConfig = async () => {
  try {
    const res = await getHasBindCloudinary()
    if (res.success) {
      hasCloudinaryConfig.value = !!res.data
    } else {
      toast.error(res.msg || '获取图床配置状态失败')
    }
  } catch (error) {
    console.error('检查图床配置失败:', error)
    hasCloudinaryConfig.value = false
  }
}

// 步骤控制
const nextStep = () => {
  if (activeStep.value < 2) {
    activeStep.value++
  }
}

const prevStep = () => {
  if (activeStep.value > 0) {
    activeStep.value--
  }
}

// 跳转到图床配置页面
const toCloudinary = () => {
  uni.navigateTo({
    url: '/pages/about/cloudpic/enpower/enpower',
  })
}

// 生成绑定口令
const generateSecret = async () => {
  try {
    toast.loading('生成口令中...')
    const res = await getBindCpWxUserSecret()
    toast.close()
    if (res.success && res.data) {
      secret.value = res.data
      message.show('请复制口令发送给收藏助手进行绑定')
    } else {
      toast.error(res.msg || '生成口令失败')
    }
  } catch (error) {
    toast.close()
    const errorMessage = extractErrorMessage(error)
    toast.error(errorMessage || '生成口令失败，请重试')
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 复制口令到剪贴板
const copySecret = () => {
  if (!secret.value) {
    toast.error('请先生成口令')
    return
  }

  wx.hideToast()
  uni.setClipboardData({
    data: secret.value,
    success: () => {
      wx.hideToast()
      toast.success('复制成功')
    },
    fail: () => {
      wx.hideToast() // Added wx.hideToast() for consistency
      toast.error('复制失败，请重试')
    },
  })
}

// 检查增强功能开关状态
const checkEnhanceStatus = async () => {
  try {
    const res = await getEnhanceSwitch()
    if (res.success) {
      enhanceEnabled.value = res.data
    } else {
      toast.error(res.msg || '获取增强功能状态失败')
    }
  } catch (error) {
    console.error('检查增强功能状态失败:', error)
    enhanceEnabled.value = false
  }
}

// 切换增强功能开关
const toggleEnhance = async (newStatus: any) => {
  try {
    // 处理 newStatus 参数
    const boolValue =
      typeof newStatus === 'object' && newStatus !== null && 'value' in newStatus
        ? newStatus.value
        : newStatus

    // 如果是尝试打开开关，需要检查是否已经配置好图床
    if (boolValue === true) {
      // 获取S3配置，检查是否已配置
      const configRes = await getS3Config()

      // 如果没有配置数据，提示用户配置
      if (!configRes.success || !configRes.data) {
        // 将开关设置回原来的状态
        enhanceEnabled.value = false

        // 显示确认对话框
        message
          .confirm({
            title: '未配置图床',
            msg: '您还未配置增强图床，是否前往配置页面？',
            confirmButtonText: '去配置',
            cancelButtonText: '取消',
          })
          .then(() => {
            // 用户点击确认，跳转到配置页面
            uni.navigateTo({
              url: '/pages/about/cloudpic/enpower/enpower',
            })
          })
          .catch(() => {
            // 用户取消，不做任何操作
          })
        return
      }
    }

    // 调用API切换增强功能开关
    const res = await enhanceSwitch(boolValue)

    if (res.success) {
      enhanceEnabled.value = boolValue
      toast.success(boolValue ? '增强功能已开启' : '增强功能已关闭')
    } else {
      // 如果失败，将开关设置回原来的状态
      enhanceEnabled.value = !boolValue
      toast.error(res.msg || '操作失败，请重试')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    toast.close()
    // 出错时将开关设置回原来的状态
    enhanceEnabled.value = !newStatus
    toast.error(errorMessage || '操作失败，请重试')
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 跳转到会员页面
const toMemberPage = () => {
  uni.navigateTo({
    url: '/pages/about/member/member',
  })
}
</script>

<style lang="scss" scoped>
page {
  min-height: 100vh;
  background-color: #fff;
}

.tr-shadow {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

:deep(.wd-button) {
  border-radius: 8px;
}

.cursor-pointer {
  cursor: pointer;
}

.step-content {
  min-height: 400rpx;
  padding: 16px 0;
}

:deep(.wd-steps) {
  --steps-active-color: #8066ff;
  --steps-inactive-color: #999;
}
/* 功能项样式 */
.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.feature-icon {
  flex-shrink: 0;
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  color: #337ea9;
}

.feature-content {
  flex: 1;
}
/* 会员权益项样式 */
.member-benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.benefit-check {
  position: relative;
  flex-shrink: 0;
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  background-color: #f3ce95;
  border-radius: 50%;
}

.benefit-check::after {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  width: 12rpx;
  height: 6rpx;
  content: '';
  border-bottom: 2px solid #fff;
  border-left: 2px solid #fff;
  transform: rotate(-45deg);
}
/* 升级按钮样式 */
.upgrade-button {
  height: 88rpx;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #8066ff 0%, #5d4dc4 100%);
  border: none;
  box-shadow: 0 8rpx 16rpx rgba(128, 102, 255, 0.3);
}
/* 口令文本样式 */
.secret-text {
  line-height: 1.5;
  word-break: break-all;
  white-space: normal;
}

.copy-btn {
  padding: 4rpx 12rpx;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.copy-btn:active {
  background-color: rgba(128, 102, 255, 0.1);
}
</style>
