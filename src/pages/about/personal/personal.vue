<route lang="json5">
{
  style: {
    navigationBarTitleText: '个人信息',
    navigationBarBackgroundColor: '#fff',
  },
  disableScroll: true,
}
</route>

<template>
  <view
    class="overflow-hidden flex flex-col items-center justify-start w-full h-100% m-0 bg-#f8f9fa"
  >
    <!-- 用户信息卡片 -->
    <view class="mt-12px w-690rpx mb-[32rpx] bg-white rounded-[16rpx] tr-shadow">
      <!-- 头像区域 -->
      <view class="flex justify-center py-[40rpx]">
        <wd-img-cropper v-model="showCropper" :src="avatarUrl" @confirm="onCropperConfirm" />
        <button
          class="avatar-wrapper relative w-[160rpx] h-[160rpx] overflow-hidden rounded-full p-0 bg-transparent"
          open-type="chooseAvatar"
          @chooseavatar="onChooseAvatar"
        >
          <image class="w-full h-full" :src="avatarUrl" mode="aspectFill" />
          <text
            class="absolute right-0 bottom-0 left-0 py-[4rpx] text-[24rpx] text-white text-center bg-[rgba(0,0,0,0.5)]"
          >
            更换头像
          </text>
        </button>
      </view>

      <!-- 分隔线 -->
      <view class="mx-[32rpx] border-b border-solid border-[#eee]"></view>

      <!-- 用户ID区域 -->
      <view class="py-[24rpx] px-[32rpx]">
        <!-- 标签单独一行居中 -->
        <text class="block mb-[16rpx] text-[24rpx] text-[#999] text-center">您的用户ID</text>
        <!-- ID和复制图标在同一行居中 -->
        <view class="flex items-center justify-center">
          <text class="text-[28rpx] text-[#333] font-mono">{{ maskedUnionId }}</text>
          <view
            class="ml-[16rpx] p-[8rpx] cursor-pointer rounded-[8rpx] active:bg-[rgba(0,0,0,0.04)]"
            @click="copyUnionId"
          >
            <wd-icon name="file-copy" size="28rpx" color="#666" />
          </view>
        </view>
      </view>
    </view>

    <!-- 个人信息表单 -->
    <view class="w-690rpx mb-[32rpx] bg-white rounded-[16rpx] tr-shadow">
      <view class="px-[32rpx]">
        <view class="py-[24rpx]">
          <text class="text-[28rpx] text-[#666] mb-[8rpx] block">昵称</text>
          <wd-input
            v-model="userInfo.nickname"
            placeholder="请输入昵称"
            no-border
            :input-type="'nickname'"
            use-suffix-slot
            custom-class="!p-0"
            @blur="onNicknameBlur"
          >
            <template #suffix>
              <wd-icon
                name="wechat"
                size="32rpx"
                color="#07c160"
                class="ml-[16rpx]"
                @click="showWechatNicknamePicker"
              />
            </template>
          </wd-input>
        </view>
        <view class="py-[24rpx]">
          <text class="text-[28rpx] text-[#666] mb-[8rpx] block">邮箱</text>
          <wd-input
            v-model="userInfo.email"
            no-border
            clear-trigger="focus"
            clearable
            placeholder="请输入邮箱"
            custom-class="!p-0"
          />
        </view>
        <view class="py-[24rpx]">
          <text class="text-[28rpx] text-[#666] mb-[8rpx] block">子账号ID</text>
          <wd-input
            v-model="userInfo.subAccountId"
            no-border
            placeholder="请输入子账号ID"
            clear-trigger="focus"
            clearable
            custom-class="!p-0"
          />
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <wd-button type="primary" plain block @click="saveUserInfo" custom-class="cut-button">
      保存修改
    </wd-button>
    <wd-gap safe-area-bottom height="0"></wd-gap>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { useToast, useMessage } from 'wot-design-uni'
import { useUserStore } from '@/store'
import { updateUserInfo, uploadAvatar } from '@/service/user/user'
import { getHasBindCloudinary } from '@/service/piccloud/cloudinary'
import { getS3Config } from '@/service/piccloud/s3'
import type { UserSimpleInfo } from '@/service/user/user'
import { onShow } from '@dcloudio/uni-app'
import { extractErrorMessage } from '@/utils'

const toast = useToast()
const message = useMessage()
const userStore = useUserStore()

// 头像相关
const showCropper = ref(false)
const avatarUrl = ref(
  'https://res.cloudinary.com/dwal1nlws/image/upload/v1740285825/wexin/mp/461865305a33adc8d4dbeae75ed08502.png',
)

// 用户信息
const userInfo = reactive({
  nickname: '',
  email: '',
  unionId: '',
  subAccountId: '',
  avatarUrl: '',
})

// 初始化用户信息
const initUserInfo = () => {
  // 从 userStore 获取用户信息
  const storeUserInfo = userStore.userInfo
  console.log('从 userStore 获取的用户信息:', storeUserInfo)

  // 更新本地用户信息
  userInfo.nickname = storeUserInfo.nickname || '游客用户'
  userInfo.email = storeUserInfo.email || ''
  userInfo.unionId = storeUserInfo.unionId || ''

  // 子账号ID存储为 subUserId
  userInfo.subAccountId = storeUserInfo.subUserId || ''

  console.log('初始化后的本地用户信息:', userInfo)

  // 更新头像
  if (storeUserInfo.avatar) {
    avatarUrl.value = storeUserInfo.avatar
    userInfo.avatarUrl = storeUserInfo.avatar
  } else {
    userInfo.avatarUrl = avatarUrl.value
  }
}

// 页面生命周期钩子
onShow(() => {
  console.log('App Show')
  initUserInfo()
})

// 定义页面生命周期钩子
defineExpose({
  onShow,
})

// 头像裁剪确认
const onCropperConfirm = async (event: any) => {
  try {
    // 显示加载提示
    toast.loading('上传中...')

    // 获取裁剪后的临时文件路径
    const tempFilePath = event.url

    // 调用上传头像API
    const { data } = await uploadAvatar(tempFilePath, userInfo.unionId)

    toast.close()

    if (data) {
      // 更新本地头像URL
      avatarUrl.value = data
      userInfo.avatarUrl = data
      showCropper.value = false

      // 更新本地存储中的用户信息
      const updatedUserInfo = {
        ...userStore.userInfo,
        avatar: data,
      }
      userStore.setUserInfo(updatedUserInfo)

      // 更新用户信息刷新时间
      userStore.updateUserInfoRefreshTime()

      message.show('头像上传成功')
    } else {
      toast.error('头像上传失败')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    toast.close()
    toast.error(errorMessage || '头像上传失败，请重试')
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 微信头像选择器回调
const onChooseAvatar = async (e: any) => {
  // 先判断用户是否配置图床或高级图床，如果没有配置，则提示先配置图床
  try {
    // 显示加载提示
    toast.loading('检查图床配置...')

    // 先检查是否配置了基础图床（Cloudinary）
    const cloudinaryRes = await getHasBindCloudinary()
    const hasCloudinary = cloudinaryRes.success && !!cloudinaryRes.data

    // 如果基础图床已配置，就不再检查高级图床
    let hasS3 = false
    if (!hasCloudinary) {
      // 基础图床未配置，检查高级图床（S3兼容图床）
      const s3Res = await getS3Config()
      hasS3 = s3Res.success && !!s3Res.data
    }

    if (!hasCloudinary && !hasS3) {
      // 如果两种图床都没有配置，弹出提示
      message
        .confirm({
          title: '未配置图床',
          msg: '上传头像需要先配置图床，是否前往配置页面？',
          confirmButtonText: '去配置',
          cancelButtonText: '取消',
        })
        .then(() => {
          // 用户点击确认，跳转到图床配置页面
          uni.navigateTo({
            url: '/pages/about/cloudpic/cloudpic',
          })
        })
        .catch(() => {
          // 用户点击取消，不做任何操作
        })
      return // 中断后续操作
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    toast.close()
    toast.error(errorMessage || '检查图床配置失败，请重试')
  }

  try {
    console.log('选择头像回调:', e)
    toast.close()
    // 显示加载提示
    toast.loading('上传中...')

    // 获取微信选择的头像临时文件路径
    const { avatarUrl: tempFilePath } = e.detail

    // 调用上传头像API
    const { data } = await uploadAvatar(tempFilePath, userInfo.unionId)
    toast.close()

    if (data) {
      // 更新本地头像URL
      avatarUrl.value = data
      userInfo.avatarUrl = data

      // 更新本地存储中的用户信息
      const updatedUserInfo = {
        ...userStore.userInfo,
        avatar: data,
      }
      userStore.setUserInfo(updatedUserInfo)

      // 更新用户信息刷新时间
      userStore.updateUserInfoRefreshTime()

      message.show('头像上传成功')
    } else {
      toast.error('头像上传失败')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    console.error('头像上传失败:', errorMessage)
    toast.close()
    toast.error(errorMessage || '头像上传失败，请重试')
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 昵称输入框失焦回调
const onNicknameBlur = (e: any) => {
  console.log(e)
  if (e.value && e.value !== userInfo.nickname) {
    message.show('昵称已更新')
  }
}

// 获取脱敏后的用户ID
const maskedUnionId = computed(() => {
  const id = userInfo.unionId || ''
  if (id.length <= 6) return id // 如果ID太短，不进行脱敏

  // 保留前3位和后3位，中间用星号代替
  const prefix = id.substring(0, 8)
  const suffix = id.substring(id.length - 8)
  const maskLength = id.length - 16
  const mask = '*'.repeat(maskLength)

  return `${prefix}${mask}${suffix}`
})

// 复制用户ID（使用未脱敏的原始ID）
const copyUnionId = () => {
  wx.hideToast()
  uni.setClipboardData({
    data: userInfo.unionId,
    success: () => {
      wx.hideToast()
      toast.success('用户ID已复制')
    },
  })
}

// 保存用户信息
const saveUserInfo = async () => {
  try {
    // 显示加载提示
    toast.loading('保存中...')

    // 准备要提交的数据
    const updateData: UserSimpleInfo = {
      nickname: userInfo.nickname,
      email: userInfo.email,
      unionId: userInfo.unionId,
      subUserId: userInfo.subAccountId,
      avatar: userInfo.avatarUrl,
    }

    console.log('提交的用户数据:', updateData)

    // 调用更新API
    const { data } = await updateUserInfo(updateData)

    // 关闭加载提示
    toast.close()

    if (data) {
      // 更新成功，同步更新 userStore 中的信息
      const updatedUserInfo = {
        ...userStore.userInfo,
        nickname: userInfo.nickname,
        email: userInfo.email,
        subUserId: userInfo.subAccountId,
        avatar: userInfo.avatarUrl,
      }

      console.log('更新后的 userStore 数据:', updatedUserInfo)

      // 直接更新本地存储中的用户信息，不再设置刷新标记
      userStore.setUserInfo(updatedUserInfo)

      // 更新用户信息刷新时间，避免基于时间的刷新机制触发不必要的请求
      userStore.updateUserInfoRefreshTime()

      // 显示成功提示
      toast.success('保存成功')
    } else {
      // 更新失败
      toast.error('保存失败，请重试')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    // 关闭加载提示
    toast.close()

    console.error('保存用户信息失败:', errorMessage)
    toast.error(errorMessage || '保存失败，请重试')
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 获取微信昵称
const showWechatNicknamePicker = () => {
  // 提示用户点击输入框使用微信昵称
  message.show('点击输入框可使用微信昵称')
}
</script>

<style>
/* 头像选择器样式 */
.avatar-wrapper {
  padding: 0 !important;
  border: none !important;
}

.avatar-wrapper::after {
  border: none;
}

:deep(.cut-button) {
  width: 690rpx !important;
  height: 80rpx !important;
  margin-top: 16px !important;
  margin-bottom: 20px !important;
  font-size: 16px !important;
  line-height: 24px !important;
  border: 0.1rpx solid #337ea9 !important;
  box-shadow: #edeef1 0px 1px 7px 0px;
}
</style>
