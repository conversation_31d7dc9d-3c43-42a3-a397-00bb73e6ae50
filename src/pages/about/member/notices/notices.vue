<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '购买须知',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="overflow-hidden bg-#f8f8f8 min-h-screen flex flex-col items-center">
    <!-- 顶部标题区域 -->
    <view class="w-full flex flex-col items-center justify-center py-6 bg-white">
      <wd-text size="24px" bold text="购买须知" color="#337ea9" />
      <wd-gap height="12px"></wd-gap>
      <wd-text
        size="14px"
        text="请在购买前仔细阅读以下内容"
        color="#666"
        custom-class="w-70% text-center"
      />
    </view>

    <wd-gap height="16px"></wd-gap>

    <!-- 内容卡片 -->
    <view class="content-card tr-shadow">
      <!-- 条目1 -->
      <view class="notice-item">
        <view class="item-header">
          <view class="item-icon">
            <view class="i-solar:document-text-linear w-20px h-20px c-#337ea9"></view>
          </view>
          <wd-text size="16px" bold text="了解产品" color="#333" />
        </view>
        <view class="item-content">
          <wd-text
            size="14px"
            line-height="22px"
            color="#666"
            text="购买前务必先查阅本产品的使用说明和常见问题等文档,了解本产品的能力和不足。"
          />
        </view>
      </view>

      <!-- 条目2 -->
      <view class="notice-item">
        <view class="item-header">
          <view class="item-icon">
            <view class="i-solar:card-linear w-20px h-20px c-#337ea9"></view>
          </view>
          <wd-text size="16px" bold text="预付费原则" color="#333" />
        </view>
        <view class="item-content">
          <wd-text
            size="14px"
            line-height="22px"
            color="#666"
            text="会员服务作为一种网络虚拟产品,遵循预付费原则。服务一旦激活,即不可撤销,亦不得转让。"
          />
        </view>
      </view>

      <!-- 条目3 -->
      <view class="notice-item">
        <view class="item-header">
          <view class="item-icon">
            <view class="i-solar:server-linear w-20px h-20px c-#337ea9"></view>
          </view>
          <wd-text size="16px" bold text="服务限制" color="#333" />
        </view>
        <view class="item-content">
          <wd-text
            size="14px"
            line-height="22px"
            color="#666"
            text="由于服务器资源有限,如遇到突发情况或者流量激增时,可能触发系统保护措施进而对异常流量进行拦截控制,也可能对用户流量同样产生影响。"
          />
        </view>
      </view>

      <!-- 条目4 -->
      <view class="notice-item">
        <view class="item-header">
          <view class="item-icon">
            <view class="i-solar:video-frame-replace-line-duotone w-20px h-20px c-#337ea9"></view>
          </view>
          <wd-text size="16px" bold text="依赖性说明" color="#333" />
        </view>
        <view class="item-content">
          <wd-text
            size="14px"
            line-height="22px"
            color="#666"
            text="本工具作为一款插件产品,天然依托于宿主产品能力,如果因宿主产品调整政策导致本产品无法提供现有功能,本产品可能调整服务能力或者被迫下线。"
          />
        </view>
      </view>

      <!-- 条目5 -->
      <view class="notice-item">
        <view class="item-header">
          <view class="item-icon">
            <view class="i-solar:calendar-mark-linear w-20px h-20px c-#337ea9"></view>
          </view>
          <wd-text size="16px" bold text="订阅制说明" color="#333" />
        </view>
        <view class="item-content">
          <wd-text
            size="14px"
            line-height="22px"
            color="#666"
            text="基于以上原因,本产品不提供永久买断制方案作为常规服务售卖方案,也建议您按照订阅制购买本产品服务,您可以按照年卡、两年卡等方式购买。"
          />
        </view>
      </view>

      <!-- 条目6 -->
      <view class="notice-item">
        <view class="item-header">
          <view class="item-icon">
            <view class="i-solar:shield-user-linear w-20px h-20px c-#337ea9"></view>
          </view>
          <wd-text size="16px" bold text="退款政策" color="#333" />
        </view>
        <view class="item-content">
          <wd-text
            size="14px"
            line-height="22px"
            color="#666"
            text="若本产品无法继续运营,对于之前购买过永久会员朋友,可以按照年卡费用扣除已服务年限（不足一年按一年计算）,退还剩余会员费用。万分感谢您的支持！"
          />
        </view>
      </view>
    </view>

    <!-- 底部按钮区域 -->
    <view class="bottom-actions">
      <wd-button type="primary" custom-class="action-btn" @click="goBack">返回会员页面</wd-button>
    </view>

    <wd-gap height="30px" safe-area-bottom></wd-gap>
  </view>
</template>

<script lang="ts" setup>
const goBack = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.content-card {
  width: 690rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12px;
}

.notice-item {
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.item-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 12rpx;
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}

.item-content {
  padding-left: 48rpx;
}

.bottom-actions {
  width: 690rpx;
  margin-top: 20rpx;
  text-align: center;
}

.action-btn {
  height: 88rpx !important;
  font-size: 16px !important;
  border-radius: 12px !important;
}
</style>
