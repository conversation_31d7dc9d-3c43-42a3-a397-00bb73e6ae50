<route lang="json5">
{
  style: {
    navigationBarTitleText: '',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="overflow-hidden">
    <!-- 主内容区 -->
    <view class="min-h-screen w-full flex flex-col items-center member-bg">
      <wd-gap height="40px"></wd-gap>

      <!-- 标题区域 -->
      <view class="flex items-center">
        <wd-icon name="vip" size="24px" color="#8066FF" custom-class="mr-[8px]" />
        <wd-text size="30px" text="会员方案" color="#3a3a3a" bold custom-class="title-shadow" />
      </view>

      <!-- 广告语 -->
      <wd-gap height="20px"></wd-gap>
      <wd-text
        text="使用 NotionMpClipper,助你告别 Notion 剪藏困境,提升个人工作效率"
        color="#333"
        size="14px"
        custom-class="!w-75% text-center"
      />

      <!-- 链接区域 -->
      <view class="mt-[40rpx] w-[70%] flex justify-center gap-[60rpx]">
        <view class="flex items-center" @click="readRights">
          <wd-icon name="info-circle" size="14px" color="#8066FF" custom-class="mr-[4px]" />
          <wd-text size="14px" text="权益说明" color="#8066FF" decoration="underline" />
        </view>
        <view class="flex items-center" @click="readNotices">
          <wd-icon name="info-circle" size="14px" color="#fa4126" custom-class="mr-[4px]" />
          <wd-text size="14px" text="购买须知" color="#fa4126" decoration="underline" />
        </view>
      </view>

      <wd-gap height="30px"></wd-gap>

      <!-- 会员卡片列表 - 竖向排列 -->
      <view v-if="goodsList && goodsList.length > 0" class="w-full flex flex-col items-center">
        <view
          v-for="(item, index) in goodsList"
          :key="index"
          class="w-[690rpx] mt-[30rpx] rounded-[40rpx] bg-white-70 backdrop-blur-md flex justify-between items-center member-card"
          :class="{ 'premium-card': index > 0 }"
        >
          <view class="flex-1 flex flex-col items-start gap-[8px] py-[30rpx] pl-[30rpx]">
            <view class="flex items-center">
              <wd-text size="18px" :text="item.goodsName" color="#3a3a3a" bold />
              <!-- 永续会员显示"限时开放"，其他商品（除第一个外）显示"推荐" -->
              <view
                v-if="item.goodsName.includes('永续会员')"
                class="ml-[12rpx] bg-gradient-to-r from-[#ff6b35] to-[#ff8c42] px-[16rpx] py-[4rpx] rounded-full"
              >
                <wd-text size="12px" text="限时开放" color="#fff" />
              </view>
              <view
                v-else-if="index > 0"
                class="ml-[12rpx] bg-gradient-to-r from-[#8066FF] to-[#a58eff] px-[16rpx] py-[4rpx] rounded-full"
              >
                <wd-text size="12px" text="推荐" color="#fff" />
              </view>
            </view>
            <view class="flex items-center">
              <wd-text size="14px" text="¥" color="#fa4126" />
              <wd-text size="24px" :text="item.discount / 100" color="#fa4126" bold />
              <wd-text
                v-if="item.discount !== item.price"
                size="12px"
                :text="item.price / 100 + '元'"
                color="#999"
                decoration="line-through"
                custom-class="ml-[12rpx]"
              />
            </view>
            <!-- 永续会员显示特定描述，其他商品显示默认描述 -->
            <view v-if="item.goodsName.includes('永续会员')" class="text-size-12px text-[#666]">
              推荐按订阅付费，除非你真的喜欢的不行，哈哈。谢谢谢谢～
              <br />
              如果你已是年费会员，升级永续会员后可以联系我退年费哈
            </view>
            <view v-else class="text-size-12px text-[#666]">
              感谢您的付费支持,产品才能稳定运行下去
            </view>
          </view>
          <wd-button
            type="primary"
            size="small"
            @click="payYear(item)"
            custom-class="mr-[30rpx] !rounded-[30rpx]"
          >
            立即购买
          </wd-button>
        </view>
      </view>

      <!-- 临时关闭提示 -->
      <view
        v-else
        class="w-[690rpx] mt-[30rpx] py-[30rpx] rounded-[40rpx] bg-white-70 backdrop-blur-md member-card"
      >
        <view class="flex flex-col items-start gap-[10rpx] px-[30rpx]">
          <wd-text size="16px" text="临时关闭" color="#3a3a3a" bold />
          <wd-text size="12px" text="如需购买请添加开发者微信处理" color="#666" />
          <wd-text size="12px" text="感谢您的付费支持,产品才能稳定运行下去" color="#666" />
        </view>
      </view>

      <!-- 底部说明 -->
      <view class="mt-auto py-[60rpx] text-center w-full">
        <view>
          关注
          <wd-text size="16px" bold color="#8066FF" text="NotionHelper" />
          订阅号
        </view>
        <view class="mt-[8rpx]">
          发送:
          <wd-text size="16px" color="#fa4126" text="优惠码" />
          , 获取折扣减 {{ discount }} 元
        </view>
        <wd-gap height="16px"></wd-gap>
        <wd-text size="14px" text="您的付费支持是我不断创造的动力" />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useToast, useMessage } from 'wot-design-uni'
import { useUserStore } from '@/store'
import {
  goodsList as fetchGoodsList,
  getDiscount,
  submitOrder,
  checkPromotionCode,
  type Goods,
  type OrderRQ,
} from '@/service/order/member'

const toast = useToast()
const message = useMessage()
const userStore = useUserStore()

// 商品列表
const goodsList = ref<Goods[]>([])

// 会员特权列表 - 不在卡片中显示，但可以在权益说明页面使用
// const benefits = ref(['无限量剪藏内容', '高级格式化选项', '优先技术支持', '新功能优先体验'])

// 优惠金额
const discount = ref(0)

// 获取商品列表
const fetchGoods = async () => {
  try {
    const res = await fetchGoodsList()
    if (res.data) {
      goodsList.value = res.data
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
  }
}

// 获取优惠金额
const fetchDiscount = async () => {
  try {
    const res = await getDiscount()
    if (res.data !== undefined) {
      // 将分转换为元
      discount.value = res.data
    }
  } catch (error) {
    console.error('获取优惠金额失败:', error)
    // 使用默认值，不显示错误提示
    discount.value = 3
  }
}

// 页面显示时获取数据
onShow(() => {
  fetchGoods()
  fetchDiscount()
})

// 阅读权益说明
const readRights = () => {
  uni.navigateTo({
    url: '/pages/about/member/rights/rights',
  })
}

// 阅读购买须知
const readNotices = () => {
  uni.navigateTo({
    url: '/pages/about/member/notices/notices',
  })
}

// 支付
const payYear = async (item: Goods) => {
  try {
    // 弹出输入框让用户输入优惠码（可选）
    uni.showModal({
      title: '优惠码输入',
      content: '',
      editable: true,
      cancelText: '我再想想',
      confirmText: '确认购买',
      placeholderText: '请输入优惠码（可选）',
      success: async (res) => {
        if (res.cancel) {
          // 用户点击取消，终止下单流程
          message.alert('已取消下单')
        } else if (res.confirm) {
          const code = res.content.trim()
          if (code) {
            // 用户输入了优惠码，验证优惠码
            await verifyAndProcessOrder(item, code)
          } else {
            // 用户点击确定但没有输入优惠码，使用空优惠码继续下单
            await processOrder(item, '')
          }
        }
      },
      fail: (err) => {
        console.error('优惠码输入失败:', err)
        toast.error('操作失败，请重试')
      },
    })
  } catch (error) {
    console.error('支付失败:', error)
    toast.error('支付失败: ' + (error.message || '未知错误'))
    toast.close()
  }
}

// 验证优惠码并处理订单
const verifyAndProcessOrder = async (item: Goods, promotionCode: string) => {
  try {
    toast.loading('验证优惠码...')

    // 调用验证优惠码接口
    const res = await checkPromotionCode(promotionCode)

    // 关闭加载提示
    toast.close()

    // 判断响应码
    if (res.code !== 200) {
      // 如果响应码不是 200，展示错误信息
      console.error('验证优惠码失败:', res.msg)

      // 确保先关闭加载提示，再显示错误提示
      setTimeout(() => {
        uni.showToast({
          title: res.msg || '验证优惠码失败',
          icon: 'none',
          duration: 3000,
        })
      }, 300)

      // 询问用户是否继续下单
      uni.showModal({
        title: '优惠码验证失败',
        content: '是否继续下单？',
        confirmText: '继续',
        cancelText: '取消',
        success: async (modalRes) => {
          if (modalRes.confirm) {
            // 用户选择继续，使用空优惠码下单
            await processOrder(item, '')
          } else {
            // 用户点击取消，终止下单流程
            message.show('已取消下单')
          }
        },
      })
      return
    }

    // 如果响应码是 200，检查数据
    if (res.data) {
      // 优惠码有效，继续下单
      toast.success('优惠码验证成功')
      await processOrder(item, promotionCode)
    } else {
      // 询问用户是否继续下单
      uni.showModal({
        title: '优惠码无效',
        content: '是否继续下单？',
        confirmText: '继续',
        cancelText: '取消',
        success: async (modalRes) => {
          if (modalRes.confirm) {
            // 用户选择继续，使用空优惠码下单
            await processOrder(item, '')
          } else {
            // 用户点击取消，终止下单流程
            message.show('已取消下单')
          }
        },
      })
    }
  } catch (error) {
    console.error('验证优惠码失败:', error)
    // 关闭加载提示
    toast.close()
    // 使用 uni.showToast 替代 toast.error
    setTimeout(() => {
      uni.showToast({
        title: '验证优惠码失败，请重试',
        icon: 'none',
        duration: 3000,
      })
    }, 300)
  }
}

// 处理订单和支付逻辑
const processOrder = async (item: Goods, discountCode: string) => {
  try {
    // 显示加载提示
    toast.loading('正在创建订单...')

    // 准备订单数据
    const orderData: OrderRQ = {
      goodsNo: item.goodsNo,
      amount: item.discount,
      discountCode, // 使用用户输入的优惠码
    }

    // 调用接口创建订单
    const res = await submitOrder(orderData)

    // 关闭加载提示
    toast.close()

    // 判断响应码
    if (res.code !== 200) {
      // 如果响应码不是 200，展示错误信息
      console.error('创建订单失败:', res.msg)

      // 确保先关闭加载提示，再显示错误提示
      setTimeout(() => {
        // 使用 uni.showToast 替代 toast.error，确保错误信息能够显示
        uni.showToast({
          title: res.msg || '创建订单失败',
          icon: 'none',
          duration: 3000,
        })
      }, 300)

      return
    }

    // 如果响应码是 200，但数据为空
    if (!res.data) {
      // 使用 uni.showToast 替代 toast.error
      uni.showToast({
        title: '创建订单失败，服务器返回数据为空',
        icon: 'none',
        duration: 3000,
      })
      return
    }

    // 如果返回的是微信支付参数
    if (res.data && typeof res.data === 'object') {
      // 处理可能的参数名称差异
      const payParams: Record<string, any> = { ...res.data }

      // 如果服务器返回的是 _package 而不是 package，进行转换
      if (payParams._package && !payParams.package) {
        payParams.package = payParams._package
        delete payParams._package
      }

      console.log('支付参数:', payParams)
      // 检查必要的支付参数是否存在
      const requiredParams = ['timeStamp', 'nonceStr', 'package', 'signType', 'paySign']
      const missingParams = requiredParams.filter((param) => !payParams[param])

      if (missingParams.length > 0) {
        // 使用 uni.showToast 替代 toast.error
        uni.showToast({
          title: `支付参数缺失: ${missingParams.join(', ')}`,
          icon: 'none',
          duration: 3000,
        })
        return
      }

      // 调用微信支付
      uni.requestPayment({
        provider: 'wxpay',
        orderInfo: payParams,
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        package: payParams.package,
        signType: payParams.signType,
        paySign: payParams.paySign,
        success: function (payRes) {
          console.log('支付成功:', payRes)

          // 设置需要刷新用户信息的标记
          // 这里需要设置标记，因为支付成功后需要从服务器获取最新的会员状态
          userStore.setNeedRefreshUserInfo(true)
          // 注意：购买会员不会影响积分数据，所以不需要刷新积分数据

          // 显示确认对话框
          uni.showModal({
            title: '支付成功',
            content: '是否跳转到个人页面？',
            confirmText: '立即查看',
            cancelText: '留在当前页',
            success: (res) => {
              if (res.confirm) {
                // 用户点击确定，跳转到个人页
                uni.switchTab({
                  url: '/pages/about/about',
                })
              }
            },
          })
        },
        fail: function (err) {
          console.log('支付失败:', err)
          if (err.errMsg && err.errMsg.indexOf('cancel') !== -1) {
            message.show('您取消了支付')
          } else {
            // 使用 uni.showToast 替代 toast.error
            uni.showToast({
              title: '支付失败: ' + (err.errMsg || JSON.stringify(err)),
              icon: 'none',
              duration: 3000,
            })
          }
        },
      })
    } else {
      // 使用 uni.showToast 替代 toast.error
      uni.showToast({
        title: '服务器返回数据格式错误',
        icon: 'none',
        duration: 3000,
      })
    }
  } catch (error) {
    console.error('支付失败:', error)
    // 关闭加载提示
    toast.close()
    // 使用 uni.showToast 替代 toast.error
    setTimeout(() => {
      uni.showToast({
        title: '支付失败: ' + (error.message || '未知错误'),
        icon: 'none',
        duration: 3000,
      })
    }, 300)
  } finally {
    // 确保加载提示关闭
    toast.close()
  }
}
</script>

<style lang="scss" scoped>
page {
  min-height: 100vh;
}

.member-bg {
  background: linear-gradient(135deg, #edc0bf 0%, #c4caef 100%);
}

.bg-white-70 {
  background-color: rgba(255, 255, 255, 0.7);
}

.title-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.member-card {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.premium-card {
  border: 1px solid rgba(128, 102, 255, 0.3);
}

.border-bottom-1px {
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
</style>
