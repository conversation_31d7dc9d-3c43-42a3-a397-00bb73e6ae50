<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '会员权益',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="overflow-hidden bg-#f8f8f7 min-h-screen">
    <wd-gap height="16px"></wd-gap>

    <!-- 会员权益卡片 -->
    <view class="mx-[30rpx]">
      <view class="rights-card member-card tr-shadow">
        <view class="card-header">
          <view class="flex flex-row items-center">
            <view
              class="i-solar:medal-ribbon-bold-duotone w-[40rpx] h-[40rpx] c-#337ea9 mr-2"
            ></view>
            <wd-text size="18px" bold text="会员权益" color="#333" />
          </view>
          <view class="card-tag bg-#E7F3F8 c-#337ea9">感谢支持</view>
        </view>

        <view class="card-content">
          <view class="rights-group">
            <view class="rights-title">
              <wd-text size="15px" bold text="基础服务" color="#333" />
            </view>
            <view class="rights-item">
              <wd-img
                width="30rpx"
                height="30rpx"
                src="/static/images/rights/yes.png"
                custom-class="rights-icon"
              />
              <wd-text size="14px" custom-class="rights-text" text="剪藏次数无限制" />
            </view>
            <view class="rights-item">
              <wd-img
                width="30rpx"
                height="30rpx"
                src="/static/images/rights/yes.png"
                custom-class="rights-icon"
              />
              <wd-text size="14px" custom-class="rights-text" text="在线技术支持" />
            </view>
            <view class="rights-item">
              <wd-img
                width="30rpx"
                height="30rpx"
                src="/static/images/rights/yes.png"
                custom-class="rights-icon"
              />
              <wd-text size="14px" custom-class="rights-text" text="服务答疑群" />
            </view>
          </view>

          <view class="rights-group">
            <view class="rights-title">
              <wd-text size="15px" bold text="专属服务" color="#333" />
            </view>

            <view class="rights-item">
              <wd-img
                width="30rpx"
                height="30rpx"
                src="/static/images/rights/yes.png"
                custom-class="rights-icon"
              />
              <wd-text
                size="14px"
                custom-class="rights-text"
                text="会员期间软件基础服务及软件优化服务"
              />
            </view>
            <view class="rights-item">
              <wd-img
                width="30rpx"
                height="30rpx"
                src="/static/images/rights/yes.png"
                custom-class="rights-icon"
              />
              <wd-text size="14px" custom-class="rights-text" text="使用问题优先响应解决" />
            </view>
            <view class="rights-item">
              <wd-img
                width="30rpx"
                height="30rpx"
                src="/static/images/rights/yes.png"
                custom-class="rights-icon"
              />
              <wd-text size="14px" custom-class="rights-text" text="反馈需求优先考虑排期" />
            </view>
          </view>

          <view class="rights-group">
            <view class="rights-title">
              <wd-text size="15px" bold text="额外福利" color="#333" />
            </view>
            <view class="rights-item">
              <wd-img
                width="30rpx"
                height="30rpx"
                src="/static/images/rights/yes.png"
                custom-class="rights-icon"
              />
              <wd-text size="14px" custom-class="rights-text" text="企微收藏助手能力" />
            </view>
            <view class="rights-item">
              <wd-img
                width="30rpx"
                height="30rpx"
                src="/static/images/rights/yes.png"
                custom-class="rights-icon"
              />
              <wd-text
                size="14px"
                custom-class="rights-text"
                text="参与推广得积分,可用于充值会员或提现等"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <wd-gap height="16px"></wd-gap>

    <!-- 普通用户卡片 -->
    <view class="mx-[30rpx]">
      <view class="rights-card normal-card tr-shadow">
        <view class="card-header">
          <view class="flex flex-row items-center">
            <view class="i-solar:user-id-bold-duotone w-[40rpx] h-[40rpx] c-#696969 mr-2"></view>
            <wd-text size="18px" bold text="普通用户" color="#333" />
          </view>
          <view class="card-tag bg-#F2F3F5 c-#696969">免费</view>
        </view>

        <view class="card-content">
          <view class="rights-group">
            <view class="rights-item">
              <wd-img
                width="30rpx"
                height="30rpx"
                src="/static/images/rights/yes.png"
                custom-class="rights-icon"
              />
              <wd-text size="14px" custom-class="rights-text" text="每月免费剪藏次数 30 条" />
            </view>
            <view class="rights-item">
              <wd-img
                width="30rpx"
                height="30rpx"
                src="/static/images/rights/yes.png"
                custom-class="rights-icon"
              />
              <wd-text size="14px" custom-class="rights-text" text="在线技术支持" />
            </view>
            <view class="rights-item">
              <wd-img
                width="30rpx"
                height="30rpx"
                src="/static/images/rights/yes.png"
                custom-class="rights-icon"
              />
              <wd-text size="14px" custom-class="rights-text" text="服务答疑群" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <wd-gap height="30px"></wd-gap>
  </view>
</template>

<script lang="ts" setup>
// 页面逻辑可以在这里添加
</script>

<style lang="scss" scoped>
.rights-card {
  padding: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.member-card {
  border-top: 4rpx solid #337ea9;
}

.normal-card {
  border-top: 4rpx solid #696969;
}

.card-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 20rpx;
  margin-bottom: 16rpx;
  border-bottom: 1px solid #f2f3f5;
}

.card-tag {
  padding: 4rpx 12rpx;
  font-size: 12px;
  border-radius: 8rpx;
}

.card-content {
  padding: 8rpx 0;
}

.rights-group {
  margin-bottom: 24rpx;
}

.rights-title {
  margin-bottom: 12rpx;
}

.rights-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16rpx;
}

:deep(.rights-icon) {
  margin-right: 12rpx !important;
}

.rights-text {
  margin-left: 12rpx;
  color: #696969;
}

:deep(.tr-shadow) {
  box-shadow: #edeef1 0px 1px 7px 0px;
}
</style>
