<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'Obsidian 配置',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="obsidian-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <wd-loading size="40px" />
      <wd-text size="14px" text="检查配置中..." color="#666" custom-class="mt-2" />
    </view>

    <!-- 主要内容 -->
    <view v-else class="content-container">
      <!-- 状态检查卡片 -->
      <view class="bg-white w-690rpx rounded-12px tr-shadow">
        <view class="status-header">
          <view class="flex items-center">
            <image class="w-6 h-6 mr-2" src="/static/images/about/ob.svg" mode="aspectFit" />
            <wd-text size="18px" text="配置状态检查" color="#333" bold />
          </view>
        </view>
        <!-- 会员状态 -->
        <view class="status-item">
          <view class="status-icon">
            <view
              class="w-5 h-5"
              :class="
                isMember
                  ? 'i-solar:shield-check-bold c-#4CAF50'
                  : 'i-solar:shield-cross-bold c-#f56c6c'
              "
            ></view>
          </view>
          <view class="status-content">
            <wd-text size="15px" text="会员权限" color="#333" />
            <wd-text
              size="13px"
              :text="isMember ? '已开通会员' : '需要会员权限'"
              :color="isMember ? '#4CAF50' : '#f56c6c'"
            />
          </view>
          <view v-if="!isMember" class="status-action">
            <wd-button type="primary" size="small" @click="toMemberPage">开通会员</wd-button>
          </view>
        </view>

        <!-- 图床配置状态 -->
        <view class="status-item">
          <view class="status-icon">
            <view
              class="w-5 h-5"
              :class="
                hasAdvancedPicCloud
                  ? 'i-solar:cloud-check-bold c-#4CAF50'
                  : 'i-solar:cloud-cross-bold c-#f56c6c'
              "
            ></view>
          </view>
          <view class="status-content">
            <wd-text size="15px" text="高级图床配置" color="#333" />
            <wd-text
              size="13px"
              :text="hasAdvancedPicCloud ? '已配置高级图床' : '需要配置高级图床'"
              :color="hasAdvancedPicCloud ? '#4CAF50' : '#f56c6c'"
            />
          </view>
          <view v-if="!hasAdvancedPicCloud" class="status-action">
            <wd-button type="primary" size="small" @click="toPicCloudPage">配置图床</wd-button>
          </view>
        </view>

        <!-- Obsidian 保存状态 -->
        <view class="status-item">
          <view class="status-icon">
            <view
              class="w-5 h-5"
              :class="
                toObsidianEnabled
                  ? 'i-solar:database-bold c-#4CAF50'
                  : 'i-solar:database-bold c-#f56c6c'
              "
            ></view>
          </view>
          <view class="status-content">
            <wd-text size="15px" text="打开（保存到Obsidian）开关" color="#333" />
            <wd-text
              size="13px"
              :text="toObsidianEnabled ? '已启用' : '未启用'"
              :color="toObsidianEnabled ? '#4CAF50' : '#f56c6c'"
            />
          </view>
        </view>
      </view>

      <wd-gap height="12px" />

      <!-- 配置表单 -->
      <view v-if="canConfigure">
        <!-- 路径配置 -->
        <view class="bg-white w-690rpx rounded-12px tr-shadow">
          <view class="config-header">
            <wd-text size="16px" text="图床配置" color="#333" bold />
            <wd-text size="13px" text="设置文章和资源的保存路径" color="#666" />
          </view>

          <wd-gap height="16px" />

          <wd-form :model="formData" ref="formRef" label-align="top">
            <view class="settings-group">
              <!-- 保存根路径 -->
              <view class="form-item-container">
                <view class="form-label">
                  <text>保存根路径</text>
                  <view class="help-icon" @click="showPathHelp('saveRoot')">
                    <wd-icon name="help-circle" size="18px" color="#337ea9"></wd-icon>
                  </view>
                </view>
                <wd-input
                  no-border
                  clearable
                  v-model="formData.saveRoot"
                  placeholder="例如：obclipper"
                  custom-class="form-input"
                />
                <view class="form-item-container-focus-indicator"></view>
              </view>

              <!-- 资源保存路径 -->
              <view class="form-item-container">
                <view class="form-label">
                  <text>资源保存路径</text>
                  <view class="help-icon" @click="showPathHelp('attRoot')">
                    <wd-icon name="help-circle" size="18px" color="#337ea9"></wd-icon>
                  </view>
                  <view class="ml-2 text-xs text-gray-500">(暂未启用)</view>
                </view>
                <wd-input
                  no-border
                  disabled
                  v-model="formData.attRoot"
                  placeholder="例如：ob_attachments"
                  custom-class="form-input disabled-input"
                />
                <view class="form-item-container-focus-indicator"></view>
              </view>
            </view>
          </wd-form>

          <!-- 路径验证提示 -->
          <view v-if="pathValidationError" class="validation-error">
            <view class="i-solar:danger-triangle-bold w-4 h-4 c-#f56c6c mr-1"></view>
            <wd-text size="12px" :text="pathValidationError" color="#f56c6c" />
          </view>
        </view>

        <wd-gap height="12px" />

        <!-- 属性配置 -->
        <view class="bg-white w-690rpx rounded-12px tr-shadow">
          <view class="config-header">
            <wd-text size="16px" text="页面属性配置" color="#333" bold />
            <wd-text size="13px" text="配置文章和消息的属性字段" color="#666" />
          </view>

          <wd-gap bg-color="#eee" height="1px" custom-class="w-90%" />

          <!-- 文章属性配置项 -->
          <view
            class="w-full h-auto py-3 flex flex-row justify-between items-center"
            hover-class="hover-item"
            @click="toArticleConfigPage"
          >
            <view class="flex flex-row items-center">
              <view class="ml-40rpx i-solar:document-text-bold w-20px h-20px"></view>
              <view class="ml-3 flex flex-col items-start justify-start">
                <wd-text text="文章属性配置" size="14px" custom-class="!c-#333"></wd-text>
              </view>
            </view>
            <view class="mr-40rpx flex flex-row items-center justify-center">
              <image class="w-24px h-24px" src="/static/images/arrow-right.png"></image>
            </view>
          </view>

          <wd-gap bg-color="#eee" height="1px" custom-class="w-90%" />

          <!-- 消息属性配置项 -->
          <view
            class="w-full h-auto py-3 flex flex-row justify-between items-center"
            hover-class="hover-item"
            @click="toMessageConfigPage"
          >
            <view class="flex flex-row items-center">
              <view class="ml-40rpx i-solar:chat-round-dots-bold w-20px h-20px"></view>
              <view class="ml-3 flex flex-col items-start justify-start">
                <wd-text text="消息属性配置" size="14px" custom-class="!c-#333"></wd-text>
              </view>
            </view>
            <view class="mr-40rpx flex flex-row items-center justify-center">
              <image class="w-24px h-24px" src="/static/images/arrow-right.png"></image>
            </view>
          </view>
        </view>

        <wd-gap height="32px" />

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <wd-button
            type="primary"
            block
            custom-class="action-btn mb-3"
            @click="saveConfig"
            :disabled="!canSave"
          >
            保存配置
          </wd-button>

          <wd-button
            type="info"
            block
            custom-class="action-btn"
            @click="copyConfig"
            :disabled="!toObsidianEnabled"
          >
            复制 Obsidian 配置
          </wd-button>
        </view>
      </view>

      <!-- 无法配置的提示 -->
      <view v-else class="cannot-configure bg-white w-690rpx rounded-12px tr-shadow">
        <view class="i-solar:info-circle-bold w-16 h-16 c-#909399 mb-4"></view>
        <wd-text size="16px" text="无法进行配置" color="#333" bold custom-class="mb-2" />
        <wd-text
          size="14px"
          text="请先完成会员开通和高级图床配置"
          color="#666"
          custom-class="text-center"
        />
      </view>
    </view>

    <wd-gap height="30px" safe-area-bottom />

    <!-- 帮助弹窗 -->
    <wd-popup v-model="showHelp" position="bottom" custom-class="help-popup">
      <view class="help-content">
        <view class="help-header">
          <view class="help-title">
            <view class="help-title-icon">
              <view class="i-solar:info-circle-bold w-4 h-4 c-white"></view>
            </view>
            <wd-text size="16px" text="字段说明" color="#333" bold />
          </view>
          <view class="help-close" @click="showHelp = false">
            <view class="i-solar:close-circle-bold w-5 h-5 c-#666"></view>
          </view>
        </view>
        <wd-text size="14px" :text="currentPathHelp" color="#666" custom-class="leading-relaxed" />
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useToast, useMessage } from 'wot-design-uni'
import { useUserStore } from '@/store'
import { extractErrorMessage } from '@/utils'
import {
  getObsidianConfig,
  saveObsidianConfig,
  type ObsidianConfig,
} from '@/service/about/obsidian'
import { getS3Config } from '@/service/piccloud/s3'
import { getCustomConfig, type CustomConfig } from '@/service/user/config'

const toast = useToast()
const message = useMessage()
const userStore = useUserStore()

// 页面状态
const loading = ref(true)
const hasAdvancedPicCloud = ref(false)
const toObsidianEnabled = ref(false)
const showHelp = ref(false)
const currentPathHelp = ref('')

// 表单数据
const formData = reactive({
  s3Id: '',
  saveRoot: '',
  attRoot: '',
})

// 表单验证规则
const rules = {
  saveRoot: [
    { required: true, message: '请输入文章保存路径' },
    {
      validator: (value: string) => {
        if (value === 'mpclipper') {
          return '文章保存路径不能是 mpclipper'
        }
        if (value === formData.attRoot) {
          return '文章保存路径不能与资源保存路径相同'
        }
        return true
      },
    },
  ],
  attRoot: [
    { required: true, message: '请输入资源保存路径' },
    {
      validator: (value: string) => {
        if (value === formData.saveRoot) {
          return '资源保存路径不能与文章保存路径相同'
        }
        return true
      },
    },
  ],
}

// 图床选项（这里需要根据实际的 S3 配置来动态生成）
const s3Options = ref([{ value: '1', label: '默认图床配置' }])

// 计算属性：是否是会员
const isMember = computed(() => {
  const userType = userStore.userInfo.userType
  const mainUserType = userStore.userInfo.mainUserVo?.userType
  return (
    userType === '年卡会员' ||
    userType === '永久会员' ||
    mainUserType === '年卡会员' ||
    mainUserType === '永久会员'
  )
})

// 计算属性：是否可以进行配置
const canConfigure = computed(() => {
  return isMember.value && hasAdvancedPicCloud.value
})

// 计算属性：路径验证错误信息
const pathValidationError = computed(() => {
  if (!formData.saveRoot || !formData.attRoot) return ''

  if (formData.saveRoot === 'mpclipper') {
    return '文章保存路径不能是 mpclipper'
  }

  if (formData.saveRoot === formData.attRoot) {
    return '文章保存路径和资源保存路径不能相同'
  }

  return ''
})

// 计算属性：是否可以保存
const canSave = computed(() => {
  return formData.saveRoot && formData.attRoot && formData.s3Id && !pathValidationError.value
})

// 页面初始化
onMounted(() => {
  initPageData()
})

/**
 * 初始化页面数据
 */
const initPageData = async () => {
  try {
    loading.value = true

    // 并行检查各种配置状态
    await Promise.all([checkAdvancedPicCloud(), checkToObsidianConfig()])
  } catch (error) {
    console.error('初始化页面数据失败:', error)
    const errorMessage = extractErrorMessage(error)
    toast.error(errorMessage || '加载数据失败，请重试')
  } finally {
    loading.value = false
  }
}

/**
 * 检查高级图床配置
 */
const checkAdvancedPicCloud = async () => {
  try {
    const res = await getS3Config()
    hasAdvancedPicCloud.value = res.success && !!res.data

    // 如果有配置，设置默认的 s3Id
    if (hasAdvancedPicCloud.value) {
      formData.s3Id = '1' // 这里应该根据实际返回的数据设置
    }
  } catch (error) {
    console.error('检查高级图床配置失败:', error)
    hasAdvancedPicCloud.value = false
  }
}

/**
 * 检查 toObsidian 配置状态
 */
const checkToObsidianConfig = async () => {
  try {
    const res = await getCustomConfig()
    toObsidianEnabled.value = res.success && !!res.data && res.data.toObsidian

    // 同时检查并填充 Obsidian 配置数据
    try {
      const obsidianRes = await getObsidianConfig()
      if (obsidianRes.success && obsidianRes.data) {
        const config = obsidianRes.data
        formData.s3Id = config.s3Id?.toString() || ''
        formData.saveRoot = config.saveRoot || ''
        formData.attRoot = config.attRoot || ''
      }
    } catch (obsidianError) {
      console.error('获取 Obsidian 配置数据失败:', obsidianError)
    }
  } catch (error) {
    console.error('检查 toObsidian 配置失败:', error)
    toObsidianEnabled.value = false
  }
}

/**
 * 保存配置
 */
const saveConfig = async () => {
  try {
    // 验证表单
    if (pathValidationError.value) {
      toast.error(pathValidationError.value)
      return
    }

    toast.loading('保存配置中...')

    const config: ObsidianConfig = {
      s3Id: parseInt(formData.s3Id),
      saveRoot: formData.saveRoot,
      attRoot: formData.attRoot,
    }

    const res = await saveObsidianConfig(config)

    // 先关闭加载状态
    toast.close()

    if (res.success) {
      // 配置保存成功，但不需要更新 toObsidianEnabled 状态
      // 因为这个状态由用户在自定义配置页面控制
      toast.success('配置保存成功')
    } else {
      toast.error(res.msg || '保存配置失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    // 先关闭加载状态
    toast.close()
    const errorMessage = extractErrorMessage(error)
    toast.error(errorMessage || '保存配置失败')
  }
}

/**
 * 复制 Obsidian 配置
 */
const copyConfig = async () => {
  try {
    // 获取当前配置和 S3 配置
    const [obsidianRes, s3Res] = await Promise.all([getObsidianConfig(), getS3Config()])

    if (!obsidianRes.success || !obsidianRes.data) {
      toast.error('获取 Obsidian 配置失败')
      return
    }

    if (!s3Res.success || !s3Res.data) {
      toast.error('获取 S3 配置失败')
      return
    }

    // 构建 JSON 配置对象
    const obsidianConfig = obsidianRes.data
    const s3Config = s3Res.data

    const configJson = {
      autoSync: false, // 默认为 false
      autoSyncInterval: 5, // 默认为 5
      s3AccessKey: s3Config.accessKey, // s3.accessKey
      lastSyncTimestamp: 0, // 默认为 0
      s3Endpoint: s3Config.endpoint, // s3.endpoint
      syncFolder: '', // 默认为''
      s3AccessSecret: s3Config.accessSecret, // s3.accessSecret
      lastSyncTime: '', // 默认为 ''
      s3Region: s3Config.region, // s3.region
      s3BucketName: s3Config.bucketName, // s3.bucketName
      s3FilePath: obsidianConfig.saveRoot, // obsidian 配置的 saveRoot
    }

    // 将 JSON 对象转换为格式化的字符串
    const configText = JSON.stringify(configJson, null, 2)

    // 复制到剪贴板
    uni.setClipboardData({
      data: configText,
      success: () => {
        // 弹框提醒用户注意事项
        message.confirm({
          title: '配置复制成功',
          msg: '配置已复制到剪贴板，仅供初次导入配置使用。\n\n请注意：\n1. 根据需要修改同步目录 (syncFolder)\n2. 根据需要修改上次同步时间戳 (lastSyncTimestamp)',
          confirmButtonText: '我知道了',
          showCancelButton: false,
        })
      },
      fail: () => {
        toast.error('复制失败')
      },
    })
  } catch (error) {
    console.error('复制配置失败:', error)
    const errorMessage = extractErrorMessage(error)
    toast.error(errorMessage || '复制配置失败')
  }
}

/**
 * 跳转到会员页面
 */
const toMemberPage = () => {
  uni.navigateTo({
    url: '/pages/about/member/member',
  })
}

/**
 * 跳转到图床配置页面
 */
const toPicCloudPage = () => {
  uni.navigateTo({
    url: '/pages/about/cloudpic/enpower/enpower',
  })
}

/**
 * 跳转到文章属性配置页面
 */
const toArticleConfigPage = () => {
  uni.navigateTo({
    url: '/pages/about/obsidian/article-config/article-config',
  })
}

/**
 * 跳转到消息属性配置页面
 */
const toMessageConfigPage = () => {
  uni.navigateTo({
    url: '/pages/about/obsidian/message-config/message-config',
  })
}

/**
 * 显示路径帮助信息
 */
const showPathHelp = (field: string) => {
  const helpMap: Record<string, string> = {
    saveRoot: '设置文章保存的根目录路径，建议使用英文名称，如：obclipper、articles 等',
    attRoot:
      '设置资源文件（图片、附件）保存的目录路径。注意：该功能暂未启用，当前版本中资源文件会自动保存到合适的位置。',
  }

  currentPathHelp.value = helpMap[field] || ''
  showHelp.value = true
}
</script>

<style lang="scss" scoped>
.obsidian-container {
  min-height: 100vh;
  padding: 0 30rpx;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #f8f8f7;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

.content-container {
  display: flex;
  flex-direction: column;
  align-items: center; /* 让所有子元素水平居中 */
  width: 100%;
  padding-top: 12px;
}

.status-header {
  padding: 20px 16px 16px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 6px 16px;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.status-icon {
  flex-shrink: 0;
  margin-right: 12px;
}

.status-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 4px;
}

.status-action {
  flex-shrink: 0;
}

.config-header {
  padding: 20px 20px 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.config-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0 20px 20px 20px;
}

.config-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px !important;
  border-radius: 8px !important;
}

.validation-error {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin: 8px 20px 20px 20px;
  background-color: #fef0f0;
  border-left: 3px solid #f56c6c;
  border-radius: 4px;
}

.action-buttons {
  width: 690rpx;
}

.action-btn {
  height: 48px !important;
  border-radius: 8px !important;
}

.cannot-configure {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0rpx;
  text-align: center;
}

:deep(.tr-shadow) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.wd-form) {
  padding: 0 20px 20px 20px;
}

:deep(.wd-form-item) {
  margin-bottom: 16px;
}

.settings-group {
  padding: 0;
}

.form-item-container {
  position: relative;
  margin-bottom: 32rpx;
  border-bottom: 2px solid #e0e0e0;
}

.form-label {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 4rpx;
  margin-bottom: 16rpx;
  font-size: 15px;
  font-weight: 400;
  color: #333;
  letter-spacing: 0.5px;
}

.help-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
  cursor: pointer;
}

.form-input {
  width: 100%;
  padding: 8rpx 0;
  margin-bottom: 8rpx;
}

.disabled-input {
  background-color: #f5f5f5;
  opacity: 0.6;
}

.form-item-container::after {
  position: absolute;
  bottom: -2px;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 2px;
  content: '';
  background: linear-gradient(to right, #337ea9, #4a90e2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.form-item-container:hover::after {
  opacity: 1;
}

:deep(.wd-input.is-focused) + .form-item-container-focus-indicator {
  position: absolute;
  bottom: -2px;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, #337ea9, #4a90e2);
}

.help-content {
  padding: 24px;
}

.help-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.help-title {
  display: flex;
  gap: 12px;
  align-items: center;
}

.help-title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 6px;
}

.help-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
}

:deep(.help-popup) {
  border-radius: 16px 16px 0 0;
}

.hover-item {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>
