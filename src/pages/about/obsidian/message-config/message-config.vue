<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '消息属性配置',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="message-config-container mt-4 pb-safe">
    <!-- 页面说明 -->
    <wd-card custom-class="header-card">
      <view class="header-section">
        <view class="flex items-center">
          <view class="header-icon">
            <view class="i-solar:chat-round-dots-bold w-6 h-6 c-white"></view>
          </view>
          <wd-text size="18px" text="消息属性配置" color="#333" bold />
        </view>
        <wd-text
          size="14px"
          text="配置消息剪藏时的属性字段，这些字段将用于 Obsidian 的 Front Matter"
          color="#666"
          custom-class="leading-relaxed"
        />
      </view>
    </wd-card>

    <!-- 配置表单 -->
    <wd-card class="tr-shadow rounded-lg">
      <wd-form :model="formData" label-align="top">
        <view class="settings-group">
          <!-- 分类 -->
          <view class="form-item-container">
            <view class="form-label">
              <text>分类字段</text>
              <view class="help-icon" @click="showFieldHelp('category')">
                <wd-icon name="help-circle" size="18px" color="#337ea9"></wd-icon>
              </view>
            </view>
            <wd-input
              no-border
              clearable
              v-model="formData.category"
              placeholder="例如：category"
              custom-class="form-input"
            />
            <view class="form-item-container-focus-indicator"></view>
          </view>

          <!-- 标签 -->
          <view class="form-item-container">
            <view class="form-label">
              <text>标签字段</text>
              <view class="help-icon" @click="showFieldHelp('tags')">
                <wd-icon name="help-circle" size="18px" color="#337ea9"></wd-icon>
              </view>
            </view>
            <wd-input
              no-border
              clearable
              v-model="formData.tags"
              placeholder="例如：tags"
              custom-class="form-input"
            />
            <view class="form-item-container-focus-indicator"></view>
          </view>

          <!-- 创建时间 -->
          <view class="form-item-container">
            <view class="form-label">
              <text>创建时间字段</text>
              <view class="help-icon" @click="showFieldHelp('createTime')">
                <wd-icon name="help-circle" size="18px" color="#337ea9"></wd-icon>
              </view>
            </view>
            <wd-input
              no-border
              clearable
              v-model="formData.createTime"
              placeholder="例如：createTime"
              custom-class="form-input"
            />
            <view class="form-item-container-focus-indicator"></view>
          </view>
        </view>
      </wd-form>
    </wd-card>

    <!-- 操作按钮 -->
    <view class="form-footer">
      <wd-button type="info" block @click="resetToDefault" custom-class="tr-shadow w-40%">
        恢复默认
      </wd-button>
      <wd-button
        type="primary"
        block
        @click="saveConfig"
        custom-class="tr-shadow w-40%"
        :disabled="!isFormValid"
      >
        保存配置
      </wd-button>
    </view>

    <!-- 字段帮助弹窗 -->
    <wd-popup v-model="showHelp" position="bottom" custom-class="help-popup" safe-area-inset-bottom>
      <view class="help-content">
        <view class="help-header">
          <view class="help-title">
            <view class="help-title-icon">
              <view class="i-solar:info-circle-bold w-5 h-5 c-#666"></view>
            </view>
            <wd-text size="16px" :text="currentFieldHelp.title" color="#333" bold />
          </view>
          <view class="help-close" @click="showHelp = false">
            <view class="i-solar:close-circle-bold w-6 h-6 c-#666"></view>
          </view>
        </view>

        <wd-gap height="16px" />

        <wd-text
          size="14px"
          :text="currentFieldHelp.description"
          color="#666"
          custom-class="leading-relaxed"
        />

        <view v-if="currentFieldHelp.example" class="help-example">
          <wd-text size="13px" text="示例：" color="#333" bold />
          <view class="example-code">
            <wd-text
              size="13px"
              :text="currentFieldHelp.example"
              color="#337ea9"
              custom-class="font-mono"
            />
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useToast, useMessage } from 'wot-design-uni'
import { extractErrorMessage } from '@/utils'
import {
  getObsidianConfig,
  saveObsidianConfig,
  type MessageConfig,
  type ObsidianConfig,
} from '@/service/about/obsidian'

const toast = useToast()
const message = useMessage()

// 表单数据
const formData = reactive<MessageConfig>({
  category: 'category',
  tags: 'tags',
  createTime: 'createTime',
})

// 帮助弹窗相关
const showHelp = ref(false)
const currentFieldHelp = ref({
  title: '',
  description: '',
  example: '',
})

// 字段帮助信息
const fieldHelpMap = {
  category: {
    title: '分类字段',
    description: '用于标识消息的分类或类别，比如笔记、想法、待办等。',
    example: 'category: 文本',
  },
  tags: {
    title: '标签字段',
    description: '消息的标签，用于标记和检索，通常是数组格式。',
    example: 'tags: [重要, 工作, 学习]',
  },
  createTime: {
    title: '创建时间字段',
    description: '消息被剪藏的时间，通常是当前时间。',
    example: 'createTime: 2024-01-15 10:30:00',
  },
}

// 计算属性：表单是否有效
const isFormValid = computed(() => {
  return Object.values(formData).every((value) => value.trim() !== '')
})

// 页面初始化
onMounted(() => {
  loadConfig()
})

/**
 * 加载配置
 */
const loadConfig = async () => {
  try {
    const res = await getObsidianConfig()
    if (res.success && res.data && res.data.msgInfo) {
      try {
        const config = JSON.parse(res.data.msgInfo) as MessageConfig
        Object.assign(formData, config)
      } catch (error) {
        console.error('解析消息配置失败:', error)
      }
    }
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

/**
 * 保存配置
 */
const saveConfig = async () => {
  try {
    toast.loading('保存配置中...')

    // 只提交 msgInfo 字段
    const config = {
      msgInfo: JSON.stringify(formData),
    }

    const res = await saveObsidianConfig(config)

    // 关闭loading提示
    toast.close()

    if (res.success) {
      toast.success('消息属性配置保存成功')
    } else {
      toast.error(res.msg || '保存配置失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    // 关闭loading提示
    toast.close()
    const errorMessage = extractErrorMessage(error)
    toast.error(errorMessage || '保存配置失败')
  }
}

/**
 * 恢复默认配置
 */
const resetToDefault = () => {
  Object.assign(formData, {
    category: 'category',
    tags: 'tags',
    createTime: 'createTime',
  })
  toast.success('已恢复默认配置')
}

/**
 * 显示字段帮助
 */
const showFieldHelp = (field: keyof typeof fieldHelpMap) => {
  currentFieldHelp.value = fieldHelpMap[field]
  showHelp.value = true
}
</script>

<style lang="scss" scoped>
.message-config-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  min-height: 100vh;
  overflow: hidden;
  background-color: #f5f7fa;
}

:deep(.wd-card) {
  width: 690rpx;
  padding: 0px !important;
  overflow: hidden;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.header-card {
  width: 690rpx;
  margin-top: 16px;
  margin-bottom: 16px;
  background-color: #fff;
  border-radius: 16rpx;
}

.header-section {
  padding: 0px 16px 0 16px;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-right: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
}

.settings-group {
  padding: 0 16px;
}

.form-item-container {
  position: relative;
  margin-bottom: 32rpx;
  border-bottom: 2px solid #e0e0e0;
}

.form-label {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 4rpx;
  margin-bottom: 16rpx;
  font-size: 15px;
  font-weight: 500;
  color: #333;
  letter-spacing: 0.5px;
}

.help-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
  cursor: pointer;
}

.form-input {
  width: 100%;
  padding: 8rpx 0;
  margin-bottom: 8rpx;
}

.form-item-container::after {
  position: absolute;
  bottom: -2px;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 2px;
  content: '';
  background: linear-gradient(to right, #337ea9, #4a90e2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.form-item-container:hover::after {
  opacity: 1;
}

:deep(.wd-input.is-focused) + .form-item-container-focus-indicator {
  position: absolute;
  bottom: -2px;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, #337ea9, #4a90e2);
}

.form-footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: 32rpx;
}

:deep(.wd-button) {
  height: 88rpx;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.wd-button--primary) {
  background-color: #337ea9;
  border-color: #337ea9;
}

:deep(.wd-button--primary:active) {
  background-color: #2a6a8f;
  border-color: #2a6a8f;
  transform: translateY(2rpx);
}

:deep(.wd-button--info) {
  color: #666;
  background-color: #f5f7fa;
  border-color: #ddd;
}

:deep(.wd-button--info:active) {
  color: #555;
  background-color: #e8e8e8;
  border-color: #ccc;
  transform: translateY(2rpx);
}

.help-content {
  padding: 24px;
}

.help-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.help-title {
  display: flex;
  gap: 12px;
  align-items: center;
}

.help-title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #e9ecef;
  border-radius: 6px;
}

.help-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
}

.help-example {
  padding: 16px;
  margin-top: 16px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.example-code {
  margin-top: 8px;
}

:deep(.help-popup) {
  border-radius: 16px 16px 0 0;
}

.font-mono {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.leading-relaxed {
  line-height: 1.6;
}
</style>
