<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的积分',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="overflow-hidden bg-#f8f8f7 pb-safe">
    <!-- 会员内容 -->
    <template v-if="isMember">
      <!-- 积分概览卡片 -->
      <view class="w-full flex flex-col items-center justify-center mt-4">
        <view class="tr-shadow pt-4 pb-4 bg-white border-rd w-690rpx flex flex-col">
          <!-- 推广码展示 -->
          <view class="flex flex-row items-center justify-between px-4 mb-4">
            <view class="flex flex-row items-center">
              <wd-icon name="share" size="20px" color="#337ea9" />
              <wd-text text="我的推广码" size="16px" custom-class="ml-2 font-bold" />
            </view>
            <view class="flex flex-row items-center">
              <wd-tag color="#337ea9" bg-color="#E7F3F8" custom-class="!text-size-16px">
                {{ pointsData.promotionCode }}
              </wd-tag>
              <wd-button size="small" plain custom-class="ml-2" @click="copyPromotionCode">
                复制
              </wd-button>
            </view>
          </view>

          <wd-gap bg-color="#eee" height="1px" custom-class="w-90% mx-auto"></wd-gap>

          <!-- 积分统计 -->
          <view class="mt-4">
            <wd-row custom-class="w-full">
              <wd-col :span="8">
                <stat-item
                  :value="pointsData.sum"
                  label="总积分"
                  value-color="#333"
                  value-size="22px"
                />
              </wd-col>
              <wd-col :span="8">
                <stat-item
                  :value="pointsData.availableSum"
                  label="可用积分"
                  value-color="#00c48f"
                  value-size="22px"
                />
              </wd-col>
              <wd-col :span="8">
                <stat-item
                  :value="pointsData.useSum"
                  label="已用积分"
                  value-color="#337ea9"
                  value-size="22px"
                />
              </wd-col>
            </wd-row>
          </view>
        </view>
      </view>

      <!-- 积分明细列表 -->
      <view class="w-full flex flex-col items-center justify-center mt-4">
        <view class="tr-shadow pt-4 pb-4 bg-white border-rd w-690rpx flex flex-col">
          <view class="flex flex-row items-center px-4 mb-4">
            <wd-icon name="list" size="20px" color="#337ea9" />
            <wd-text text="积分明细" size="16px" custom-class="ml-2 font-bold" />
          </view>

          <wd-gap bg-color="#eee" height="1px" custom-class="w-90% mx-auto"></wd-gap>

          <!-- 积分明细列表 -->
          <view v-if="creditItems.length === 0" class="flex justify-center items-center py-8">
            <wd-text text="暂无积分记录" size="14px" color="#999" />
          </view>

          <view v-else class="credit-list">
            <view
              v-for="(item, index) in creditItems"
              :key="index"
              class="credit-item flex flex-row items-center justify-between px-4 py-3"
              :class="{ 'border-bottom': index !== creditItems.length - 1 }"
            >
              <view class="flex flex-col">
                <view class="flex flex-row items-center">
                  <wd-text text="推广对象ID:" size="14px" color="#666" />
                  <wd-text :text="item.objectId.toString()" size="14px" custom-class="ml-1" />
                </view>
                <wd-text
                  :text="formatDate(item.gmtCreate)"
                  size="12px"
                  color="#999"
                  custom-class="mt-1"
                />
              </view>
              <view class="flex flex-row items-center">
                <wd-tag
                  :color="getStatusColor(item.status)"
                  :bg-color="getStatusBgColor(item.status)"
                >
                  {{ getStatusText(item.status) }}
                </wd-tag>
                <wd-text
                  :text="`+${item.credit}`"
                  size="16px"
                  color="#00c48f"
                  custom-class="ml-2 font-bold"
                />
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>

    <!-- 非会员提示页面 -->
    <template v-else>
      <view class="w-full flex flex-col items-center justify-start pt-4">
        <!-- 顶部图标和标题 -->
        <view class="i-solar:crown-bold w-180rpx h-180rpx c-#337ea9 mb-30rpx"></view>
        <wd-text size="22px" text="会员专享功能" color="#333" bold custom-class="mb-[16rpx]" />
        <wd-text
          size="16px"
          text="积分模块仅对会员用户开放"
          color="#666"
          custom-class="mb-[40rpx]"
        />

        <!-- 会员权益卡片 -->
        <view class="tr-shadow pt-6 pb-6 bg-white border-rd w-630rpx flex flex-col mb-6">
          <view class="flex flex-row items-center justify-start px-4 mb-4">
            <view class="i-solar:stars-outline w-22px h-22px c-#337ea9"></view>
            <wd-text text="积分功能介绍" size="18px" custom-class="ml-2 font-bold" />
          </view>

          <wd-gap bg-color="#eee" height="1px" custom-class="w-90% mx-auto mb-4"></wd-gap>

          <!-- 权益列表 -->
          <view class="px-4">
            <view class="benefit-item">
              <view class="i-solar:check-square-outline w-16px h-16px c-#337ea9"></view>
              <wd-text text="成为会员获取专属推广码" size="15px" custom-class="ml-2" />
            </view>
            <view class="benefit-item">
              <view class="i-solar:check-square-outline w-16px h-16px c-#337ea9"></view>
              <wd-text text="Ta 人购买会员时可使用您的推广码" size="14px" custom-class="ml-2" />
            </view>
            <view class="benefit-item">
              <view class="i-solar:check-square-outline w-16px h-16px c-#337ea9"></view>
              <wd-text
                text="年度会员推广码可以优惠 5 元，获得 3 积分"
                size="14px"
                custom-class="ml-2"
              />
            </view>
            <view class="benefit-item">
              <view class="i-solar:check-square-outline w-16px h-16px c-#337ea9"></view>
              <wd-text
                text="永久会员推广码可以优惠 7 元，获得 3 积分"
                size="14px"
                custom-class="ml-2"
              />
            </view>
            <view class="benefit-item">
              <view class="i-solar:check-square-outline w-16px h-16px c-#337ea9"></view>
              <wd-text text="当前 39 积分可兑换一张年卡" size="14px" custom-class="ml-2" />
            </view>
            <view class="benefit-item">
              <view class="i-solar:check-square-outline w-16px h-16px c-#337ea9"></view>
              <wd-text text="当前满 100 积分可提现 100 元" size="14px" custom-class="ml-2" />
            </view>
          </view>
        </view>

        <!-- 升级会员按钮 -->
        <view class="w-630rpx">
          <wd-button type="primary" block custom-class="upgrade-button" @click="toMemberPage">
            <view class="flex flex-row items-center justify-center">
              <view class="i-solar:crown-bold w-[20px] h-[20px] mr-[8rpx]"></view>
              <text class="ml-2">升级会员</text>
            </view>
          </wd-button>
        </view>
      </view>
    </template>

    <wd-gap safe-area-bottom height="20"></wd-gap>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useToast } from 'wot-design-uni'
import { getPromotionData, creditList, type Points, type Item } from '@/service/points/points'
import StatItem from '@/pages/about/components/stat-item.vue'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'PointsPage',
})

const toast = useToast()
const userStore = useUserStore()

// 判断用户是否是会员
const isMember = computed(() => {
  const userType = userStore.userInfo.userType
  const mainUserType = userStore.userInfo.mainUserVo?.userType

  return (
    userType === '年卡会员' ||
    userType === '永久会员' ||
    mainUserType === '年卡会员' ||
    mainUserType === '永久会员'
  )
})

// 积分数据
const pointsData = ref<Points>({
  promotionCode: '',
  sum: 0,
  useSum: 0,
  availableSum: 0,
  shareCount: 0,
})

// 积分明细列表
const creditItems = ref<Item[]>([])

// 获取积分数据
const fetchPointsData = async () => {
  if (!isMember.value) return

  try {
    const res = await getPromotionData()
    if (res.data) {
      pointsData.value = res.data
    }
  } catch (error) {
    toast.error('获取积分数据失败')
    console.error('获取积分数据失败', error)
  }
}

// 获取积分明细
const fetchCreditList = async () => {
  if (!isMember.value) return

  try {
    const res = await creditList()
    if (res.data) {
      creditItems.value = res.data
    }
  } catch (error) {
    toast.error('获取积分明细失败')
    console.error('获取积分明细失败', error)
  }
}

// 复制推广码
const copyPromotionCode = () => {
  wx.hideToast()
  uni.setClipboardData({
    data: pointsData.value.promotionCode,
    success: () => {
      wx.hideToast()
      toast.success('推广码已复制')
    },
  })
}

// 跳转到会员页面
const toMemberPage = () => {
  uni.navigateTo({
    url: '/pages/about/member/member',
  })
}

// 格式化日期
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 0:
      return '未兑换' // 未兑换
    case 1:
      return '已兑换' // 已兑换
    case 2:
      return '已过期' // 已过期
    default:
      return '未知状态'
  }
}

// 获取状态颜色
const getStatusColor = (status: number) => {
  switch (status) {
    case 0:
      return '#00c48f' // 可兑换（未兑换）为绿色
    case 1:
      return '#337ea9' // 已兑换为蓝色
    case 2:
      return '#999' // 已过期为灰色
    default:
      return '#999'
  }
}

// 获取状态背景色
const getStatusBgColor = (status: number) => {
  switch (status) {
    case 0:
      return '#e8f8f3' // 可兑换（未兑换）为浅绿色
    case 1:
      return '#E7F3F8' // 已兑换为浅蓝色
    case 2:
      return '#f5f5f5' // 已过期为浅灰色
    default:
      return '#f5f5f5'
  }
}

// 页面加载时获取数据
onShow(() => {
  // 并行执行两个请求，提高页面加载速度
  Promise.all([
    fetchPointsData().catch((error) => {
      console.error('获取积分数据失败', error)
      // 单个请求失败不影响整体流程
    }),
    fetchCreditList().catch((error) => {
      console.error('获取积分明细失败', error)
      // 单个请求失败不影响整体流程
    }),
  ]).catch((error) => {
    console.error('加载数据失败', error)
  })
})
</script>

<style>
.border-rd {
  border-radius: 12rpx;
}

.border-bottom {
  border-bottom: 1px solid #f0f0f0;
}

.credit-list {
  max-height: 800rpx;
  overflow-y: auto;
}

.credit-item {
  transition: background-color 0.3s;
}

.credit-item:active {
  background-color: #f9f9f9;
}

.benefit-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8rpx 0;
  margin-bottom: 16rpx;
}

.upgrade-button {
  height: 88rpx;
  font-size: 16px;
  font-weight: bold;
  border-radius: 44rpx;
  box-shadow: 0 6rpx 16rpx rgba(51, 126, 169, 0.2);
}
</style>
