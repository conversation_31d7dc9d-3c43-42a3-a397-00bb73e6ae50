<template>
  <wd-row custom-class="customClass">
    <wd-col v-for="(item, index) in statsItems" :key="index" :span="colSpan">
      <stat-item :value="item.value" :label="item.label" :value-color="item.valueColor" />
    </wd-col>
  </wd-row>
</template>

<script lang="ts" setup>
import StatItem from './stat-item.vue'

defineProps({
  // 数据项
  statsItems: {
    type: Array as () => Array<{ value: string | number; label: string; valueColor?: string }>,
    required: true,
  },
  // 布局相关
  colSpan: {
    type: Number,
    default: 8,
  },
  customClass: {
    type: String,
    default: 'w-full mt-2',
  },
})
</script>
