<template>
  <view class="flex flex-col items-center justify-center gap-1">
    <wd-text
      :text="value"
      :color="valueColor"
      :size="valueSize"
      :custom-class="valueClass"
      :lineHeight="valueLineHeight"
    ></wd-text>
    <wd-text
      :text="label"
      :color="labelColor"
      :size="labelSize"
      :lineHeight="labelLineHeight"
    ></wd-text>
  </view>
</template>

<script lang="ts" setup>
defineProps({
  // 数值
  value: {
    type: [String, Number],
    default: '0',
  },
  // 标签文本
  label: {
    type: String,
    default: '',
  },
  // 数值样式
  valueColor: {
    type: String,
    default: '#3a3a3a',
  },
  valueSize: {
    type: String,
    default: '20px',
  },
  valueClass: {
    type: String,
    default: 'fw-500',
  },
  valueLineHeight: {
    type: String,
    default: '28px',
  },
  // 标签样式
  labelColor: {
    type: String,
    default: '#666',
  },
  labelSize: {
    type: String,
    default: '12px',
  },
  labelLineHeight: {
    type: String,
    default: '18px',
  },
})
</script>

<style scoped>
/* 如果需要额外样式可以在这里添加 */
</style>
