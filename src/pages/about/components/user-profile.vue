<template>
  <view class="user-profile-container">
    <!-- 头像 昵称 会员标识面板 -->
    <view class="profile-content">
      <!-- 左侧头像昵称 -->
      <view class="user-info">
        <wd-img
          custom-image="!rounded-12px"
          :width="avatarSize"
          :height="avatarSize"
          :src="(userInfo as IUserInfo).avatar"
          :enable-preview="enablePreview"
        />
        <view class="user-details">
          <wd-gap :height="gapSize"></wd-gap>
          <view class="flex flex-row items-center justify-start gap-2">
            <wd-text
              :text="(userInfo as IUserInfo).nickname"
              :color="nicknameColor"
              bold
              :size="nicknameSize"
              :lineHeight="nicknameLineHeight"
            ></wd-text>
            <wd-tag
              v-if="showMemberTag"
              custom-class="member-tag"
              mark
              :color="getTagColor"
              :bg-color="getTagBgColor"
            >
              <text>{{ getMemberTagText }}</text>
            </wd-tag>
          </view>
          <wd-text
            v-if="(userInfo as IUserInfo).userType === '年卡会员'"
            :text="(userInfo as IUserInfo).endTime"
            :size="infoSize"
            :lineHeight="infoLineHeight"
          ></wd-text>
          <wd-text
            v-else
            :text="(userInfo as IUserInfo).createTime"
            :size="infoSize"
            :lineHeight="infoLineHeight"
          ></wd-text>
          <wd-gap :height="gapSize"></wd-gap>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { IUserInfo } from '@/typings'

const props = defineProps({
  userInfo: {
    type: Object as () => IUserInfo,
    required: true,
    default: () => ({}),
  },
  // 样式相关属性
  avatarSize: {
    type: String,
    default: '100rpx',
  },
  enablePreview: {
    type: Boolean,
    default: true,
  },
  nicknameColor: {
    type: String,
    default: '#3a3a3a',
  },
  nicknameSize: {
    type: String,
    default: '16px',
  },
  nicknameLineHeight: {
    type: String,
    default: '24px',
  },
  infoSize: {
    type: String,
    default: '12px',
  },
  infoLineHeight: {
    type: String,
    default: '18px',
  },
  gapSize: {
    type: String,
    default: '0rpx',
  },
  showMemberTag: {
    type: Boolean,
    default: true,
  },
  memberTagColor: {
    type: String,
    default: '#f3ce95',
  },
  memberTagBgColor: {
    type: String,
    default: '#212121',
  },
})

// 计算会员标签文本
const getMemberTagText = computed(() => {
  const userInfo = props.userInfo as IUserInfo
  if (!userInfo) return '普通用户'

  if (userInfo.userType === '永久会员' && userInfo.memberNo > 0) {
    return `永久会员 No.${userInfo.memberNo || ''}`
  } else if (userInfo.userType === '永久会员') {
    return '永久会员'
  } else if (userInfo.userType === '年卡会员') {
    return '年卡会员'
  } else {
    return '普通用户'
  }
})

// 根据用户类型计算标签文字颜色
const getTagColor = computed(() => {
  const userInfo = props.userInfo as IUserInfo
  if (!userInfo) return '#909399'

  if (userInfo.userType === '永久会员' || userInfo.userType === '年卡会员') {
    return props.memberTagColor // 会员使用金色文字
  } else {
    return '#ffffff' // 普通用户使用白色文字
  }
})

// 根据用户类型计算标签背景颜色
const getTagBgColor = computed(() => {
  const userInfo = props.userInfo as IUserInfo
  if (!userInfo) return '#e0e0e0'

  if (userInfo.userType === '永久会员' || userInfo.userType === '年卡会员') {
    return props.memberTagBgColor // 会员使用深色背景
  } else {
    return '#909399' // 普通用户使用灰色背景
  }
})
</script>

<style lang="scss" scoped>
.user-profile-container {
  width: 100%;
}

.profile-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 690rpx;
  padding: 32rpx 32rpx;
  margin: 0 auto;
}

.user-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  height: 100rpx;
  margin-left: 32rpx;
}

:deep(.member-tag) {
  padding: 0 8px !important;
  margin: 0 10px 10px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  line-height: 20px !important;
  border-radius: 12px !important;
}
</style>
