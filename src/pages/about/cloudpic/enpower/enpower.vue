<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '图床配置',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="enpower-container pb-safe">
    <!-- 平台选择 -->
    <view class="mt-4 mb-3 w-690rpx !rounded-lg tr-shadow overflow-hidden">
      <wd-select-picker
        label="选择图床平台"
        v-model="selectedPlatform"
        :columns="platformColumns"
        type="radio"
        size="large"
        align-right
        custom-class="platform-selector tr-shadow"
      ></wd-select-picker>
    </view>

    <!-- 图床配置表单 -->
    <!-- 通用图床配置表单模板 -->
    <template v-for="(platform, index) in platforms" :key="index">
      <wd-card class="tr-shadow rounded-lg" v-if="selectedPlatform === platform.value">
        <wd-form :model="platform.form" :rules="platform.rules" label-align="top">
          <view>
            <view class="settings-group">
              <!-- bucketName 输入框 -->
              <view class="form-item-container">
                <view class="form-label">
                  <text>bucketName</text>
                  <view
                    class="help-icon"
                    @click.stop="showFieldHelp('bucketName', platform)"
                    :hover-class="'none'"
                  >
                    <wd-icon name="help-circle" size="18px" color="#337ea9"></wd-icon>
                  </view>
                </view>
                <wd-input
                  no-border
                  prop="bucketName"
                  clearable
                  v-model="platform.form.bucketName"
                  placeholder="请输入 bucketName"
                  custom-class="form-input"
                />
                <view class="form-item-container-focus-indicator"></view>
              </view>

              <!-- region 输入框 -->
              <view v-if="platform.showRegion !== false" class="form-item-container">
                <view class="form-label">
                  <text>region</text>
                  <view
                    class="help-icon"
                    @click.stop="showFieldHelp('region', platform)"
                    :hover-class="'none'"
                  >
                    <wd-icon name="help-circle" size="18px" color="#337ea9"></wd-icon>
                  </view>
                </view>
                <wd-input
                  no-border
                  prop="region"
                  clearable
                  v-model="platform.form.region"
                  :placeholder="platform.regionPlaceholder || '请输入 region'"
                  custom-class="form-input"
                />
                <view class="form-item-container-focus-indicator"></view>
              </view>

              <!-- endpoint 输入框 -->
              <view v-if="platform.showEndpoint !== false" class="form-item-container">
                <view class="form-label">
                  <text>endpoint</text>
                  <view
                    class="help-icon"
                    @click.stop="showFieldHelp('endpoint', platform)"
                    :hover-class="'none'"
                  >
                    <wd-icon name="help-circle" size="18px" color="#337ea9"></wd-icon>
                  </view>
                </view>
                <wd-input
                  no-border
                  prop="endpoint"
                  clearable
                  v-model="platform.form.endpoint"
                  :placeholder="platform.endpointPlaceholder || '请输入 endpoint'"
                  custom-class="form-input"
                />
                <view class="form-item-container-focus-indicator"></view>
              </view>

              <!-- accountId 输入框 -->
              <view v-if="platform.showAccountId" class="form-item-container">
                <view class="form-label">
                  <text>accountId</text>
                  <view
                    class="help-icon"
                    @click.stop="showFieldHelp('accountId', platform)"
                    :hover-class="'none'"
                  >
                    <wd-icon name="help-circle" size="18px" color="#337ea9"></wd-icon>
                  </view>
                </view>
                <wd-input
                  no-border
                  prop="accountId"
                  clearable
                  v-model="platform.form.accountId"
                  placeholder="请输入 accountId"
                  custom-class="form-input"
                />
                <view class="form-item-container-focus-indicator"></view>
              </view>

              <!-- accessKey 输入框 -->
              <view class="form-item-container">
                <view class="form-label">
                  <text>accessKey</text>
                  <view
                    class="help-icon"
                    @click.stop="showFieldHelp('accessKey', platform)"
                    :hover-class="'none'"
                  >
                    <wd-icon name="help-circle" size="18px" color="#337ea9"></wd-icon>
                  </view>
                </view>
                <wd-input
                  no-border
                  prop="accessKey"
                  clearable
                  v-model="platform.form.accessKey"
                  :placeholder="platform.accessKeyPlaceholder || '请输入 accessKey'"
                  custom-class="form-input"
                />
                <view class="form-item-container-focus-indicator"></view>
              </view>

              <!-- accessSecret 输入框 -->
              <view class="form-item-container">
                <view class="form-label">
                  <text>accessSecret</text>
                  <view
                    class="help-icon"
                    @click.stop="showFieldHelp('accessSecret', platform)"
                    :hover-class="'none'"
                  >
                    <wd-icon name="help-circle" size="18px" color="#337ea9"></wd-icon>
                  </view>
                </view>
                <wd-input
                  no-border
                  prop="accessSecret"
                  show-password
                  clearable
                  v-model="platform.form.accessSecret"
                  :placeholder="platform.secretPlaceholder || '请输入 accessSecret'"
                  custom-class="form-input"
                />
                <view class="form-item-container-focus-indicator"></view>
              </view>

              <!-- 自定义域名 输入框 -->
              <view class="form-item-container">
                <view class="form-label">
                  <text>自定义域名</text>
                  <view
                    class="help-icon"
                    @click.stop="showFieldHelp('customDomain', platform)"
                    :hover-class="'none'"
                  >
                    <wd-icon name="help-circle" size="18px" color="#337ea9"></wd-icon>
                  </view>
                </view>
                <wd-input
                  no-border
                  prop="customDomain"
                  clearable
                  v-model="platform.form.customDomain"
                  :placeholder="getDomainPlaceholder(platform)"
                  custom-class="form-input"
                />
                <view class="form-item-container-focus-indicator"></view>
              </view>
            </view>
          </view>
        </wd-form>
      </wd-card>
    </template>

    <!-- 保存按钮 -->
    <view class="form-footer">
      <wd-button type="info" block @click="handleRemove" custom-class="tr-shadow w-40%">
        删除配置
      </wd-button>
      <wd-button type="primary" block @click="handleSubmit" custom-class="tr-shadow w-40%">
        保存配置
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { extractErrorMessage } from '@/utils'
import { onShow } from '@dcloudio/uni-app'
import { useToast, useMessage } from 'wot-design-uni'
import {
  getS3Config,
  saveS3Config,
  S3Config as BaseS3Config,
  removeS3Config,
} from '@/service/piccloud/s3'

// 扩展 S3Config 类型，增加 accountId 字段
// 这是为了处理 R2 类型的特殊情况，其中 accountId 字段实际上存储在 region 字段中
interface S3Config extends BaseS3Config {
  accountId?: string
}

const toast = useToast()
const message = useMessage()

// 状态变量
const selectedPlatform = ref('s3') // 默认选择兼容S3

// 定义平台选项数据
const platformColumns = ref([
  { value: 'oss', label: '阿里云OSS' },
  { value: 'cos', label: '腾讯云COS' },
  { value: 'minio', label: 'MinIO' },
  { value: 's3', label: '兼容S3' },
  { value: 'r2', label: 'Cloudflare R2' },
])

// 注意: 当类型是 s3 且 endpoint 包含 qiniucs.com 时，自定义域名为必填项

// 定义表单选项接口
interface FormOptions {
  showRegion?: boolean
  showEndpoint?: boolean
  showAccountId?: boolean
  customDomainRequired?: boolean
}

// 定义完整的表单数据接口
interface StorageForm {
  bucketName: string
  region?: string
  endpoint?: string
  accountId?: string
  accessKey: string
  accessSecret: string
  customDomain: string
}

// 统一图床表单数据结构
const createEmptyForm = (): StorageForm => {
  return {
    bucketName: '',
    region: '',
    endpoint: '',
    accountId: '',
    accessKey: '',
    accessSecret: '',
    customDomain: '',
  }
}

// 创建所有平台的表单数据
const aliyunForm = reactive(createEmptyForm())
const tencentForm = reactive(createEmptyForm())
const minioForm = reactive(createEmptyForm())
const s3Form = reactive(createEmptyForm())
const r2Form = reactive(createEmptyForm())

// 所有平台共用空规则对象
const emptyRules = {}

// 定义平台配置接口
interface PlatformConfig extends FormOptions {
  value: string
  form: StorageForm
  rules: any // 使用any类型代替FormRules
  tips: string
  regionPlaceholder?: string
  endpointPlaceholder?: string
  accessKeyPlaceholder?: string
  secretPlaceholder?: string
}

// 定义平台配置
const platforms: PlatformConfig[] = [
  {
    value: 'oss',
    form: aliyunForm,
    rules: emptyRules,

    tips: '地域节点格式例如：oss-cn-hangzhou',
    regionPlaceholder: '请输入region',
    endpointPlaceholder: '请输入endpoint',
    accessKeyPlaceholder: '请输入accessKey',
    secretPlaceholder: '请输入secretKey',
  },
  {
    value: 'cos',
    form: tencentForm,
    rules: emptyRules,

    tips: '区域格式例如：ap-guangzhou',
    regionPlaceholder: '请输入region',
    endpointPlaceholder: '请输入endpoint',
    accessKeyPlaceholder: '请输入secretId',
    secretPlaceholder: '请输入secretKey',
  },
  {
    value: 'minio',
    form: minioForm,
    rules: emptyRules,

    tips: 'endpoint格式例如：http://minio.example.com:9000',
    endpointPlaceholder: '请输入endpoint',
    accessKeyPlaceholder: '请输入accessKey',
    secretPlaceholder: '请输入secretKey',
    showRegion: false,
  },
  {
    value: 's3',
    form: s3Form,
    rules: emptyRules,

    tips: 'endpoint格式例如：https://s3.amazonaws.com',
    regionPlaceholder: '请输入region',
    endpointPlaceholder: '请输入endpoint',
    accessKeyPlaceholder: '请输入accessKey',
    secretPlaceholder: '请输入secretKey',
  },

  {
    value: 'r2',
    form: r2Form,
    rules: emptyRules,

    tips: '请输入Cloudflare账户ID和密钥，自定义域名必填',
    accessKeyPlaceholder: '请输入accessKey',
    secretPlaceholder: '请输入accessSecret',
    showRegion: false,
    showEndpoint: false,
    showAccountId: true,
    customDomainRequired: true,
  },
]

// 页面加载时检查状态
onShow(() => {
  // 检查是否已配置图床
  fetchAndFillConfig()
})

// 检查图床配置
const fetchAndFillConfig = async () => {
  try {
    // 获取S3配置
    const configRes = await getS3Config()
    if (configRes.success && configRes.data) {
      // 定义配置对象类型
      interface ConfigData {
        type: string
        bucketName: string
        region: string
        endpoint: string
        accessKey: string
        accessSecret: string
        customDomain: string
      }

      let config: ConfigData | null = null

      try {
        // 解析配置数据
        if (typeof configRes.data === 'string') {
          config = JSON.parse(configRes.data) as ConfigData
        } else if (typeof configRes.data === 'object' && configRes.data !== null) {
          config = configRes.data as ConfigData
        } else {
          throw new Error('无法解析配置数据，数据类型不支持: ' + typeof configRes.data)
        }

        if (config) {
          // 设置当前选择的平台
          if (config.type) {
            // 尝试匹配图床类型
            const lowerCaseType = config.type.toLowerCase()
            // 检查是否有对应的平台配置
            const platformExists = platforms.some((p) => p.value === lowerCaseType)

            if (platformExists) {
              selectedPlatform.value = lowerCaseType
            } else {
              // 使用默认类型
              selectedPlatform.value = 's3'
            }
          }

          // 根据平台类型填充表单数据
          const currentPlatform = platforms.find((p) => p.value === selectedPlatform.value)
          if (currentPlatform) {
            // 清空当前表单数据
            clearFormData(currentPlatform.form)

            // 填充表单数据
            currentPlatform.form.bucketName = config.bucketName || ''
            currentPlatform.form.endpoint = config.endpoint || ''
            currentPlatform.form.accessKey = config.accessKey || ''
            currentPlatform.form.accessSecret = config.accessSecret || ''
            currentPlatform.form.customDomain = config.customDomain || ''

            // 特殊处理 R2 类型，其中 region 字段实际上是 accountId
            const lowerCaseType = config.type.toLowerCase()
            if (lowerCaseType === 'r2') {
              currentPlatform.form.accountId = config.region || ''
            } else {
              currentPlatform.form.region = config.region || ''
            }
          }
        }
      } catch (parseError) {
        console.error('解析配置数据失败:', parseError)
      }
    }
  } catch (error) {
    console.error('检查图床配置失败:', error)
  }
}

// 表单提交
const handleSubmit = async () => {
  try {
    // 获取当前选择的平台信息
    const currentPlatform = platforms.find((p) => p.value === selectedPlatform.value)

    if (!currentPlatform) {
      toast.error('请选择图床平台')
      return
    }

    // 检查必填字段
    if (!currentPlatform.form.bucketName) {
      toast.error('bucketName 不能为空')
      return
    }

    if (!currentPlatform.form.accessKey) {
      toast.error('accessKey 不能为空')
      return
    }

    if (!currentPlatform.form.accessSecret) {
      toast.error('accessSecret 不能为空')
      return
    }

    // 判断是否是七牛云S3兼容模式
    const isQiniuS3Mode = isQiniuMode(currentPlatform)

    // 如果是平台配置中指定了必填，或者是七牛云S3兼容模式，则必填
    if (
      (currentPlatform.customDomainRequired || isQiniuS3Mode) &&
      !currentPlatform.form.customDomain
    ) {
      if (isQiniuS3Mode) {
        toast.error('七牛云S3兼容模式下，自定义域名不能为空')
      } else {
        toast.error('自定义域名不能为空')
      }
      return
    }

    // 如果需要region，检查是否填写
    if (currentPlatform.showRegion !== false && !currentPlatform.form.region) {
      toast.error('region 不能为空')
      return
    }

    // 如果需要endpoint，检查是否填写
    if (currentPlatform.showEndpoint !== false && !currentPlatform.form.endpoint) {
      toast.error('endpoint 不能为空')
      return
    }

    // 如果需要accountId，检查是否填写
    if (currentPlatform.showAccountId && !currentPlatform.form.accountId) {
      toast.error('accountId 不能为空')
      return
    }

    // 如果是 r2, 将 endpoint 设置为 accountId,格式为：https://xxx.r2.cloudflarestorage.com
    if (currentPlatform.value === 'r2') {
      currentPlatform.form.endpoint = `https://${currentPlatform.form.accountId}.r2.cloudflarestorage.com`
    }

    // 准备提交数据
    const s3Config: S3Config = {
      type: currentPlatform.value,
      bucketName: currentPlatform.form.bucketName,
      endpoint: currentPlatform.form.endpoint || '',
      accessKey: currentPlatform.form.accessKey,
      accessSecret: currentPlatform.form.accessSecret,
      customDomain: currentPlatform.form.customDomain || '',
      region: '', // 先设置为空，后面根据平台类型设置实际值
    }

    // 特殊处理 R2 类型，将 accountId 字段设置为 region
    if (currentPlatform.value === 'r2') {
      s3Config.region = currentPlatform.form.accountId || ''
    } else {
      s3Config.region = currentPlatform.form.region || ''
    }

    // 显示加载提示
    toast.loading('正在保存配置...')

    try {
      // 保存S3配置
      const { data, code, msg, success } = await saveS3Config(s3Config)

      // 先关闭加载提示
      toast.close()

      console.log('保存配置结果:', data, code, msg, success)
      if (code === 200 && data && success) {
        // 配置保存成功
        toast.success('图床配置已保存！')
      } else {
        toast.error(msg || '配置保存失败')
      }
    } catch (saveError) {
      const errorMessage = extractErrorMessage(saveError)
      // 先关闭加载提示
      toast.close()
      toast.error(errorMessage || '保存配置失败')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    toast.error(errorMessage || '表单提交失败')
  }
}

// 删除配置
const handleRemove = async () => {
  // 显示确认对话框
  message
    .confirm({
      title: '确认删除',
      msg: '确定要删除当前配置吗？删除后将无法使用增强功能。',
    })
    .then(async () => {
      try {
        toast.loading('正在删除配置...')

        const res = await removeS3Config()

        toast.close()

        if (res.success && res.data) {
          // 清空当前选择的平台表单
          const currentPlatform = platforms.find((p) => p.value === selectedPlatform.value)
          if (currentPlatform) {
            clearFormData(currentPlatform.form)
          }

          toast.success('配置已删除')
        } else {
          toast.error(res.msg || '删除配置失败')
        }
      } catch (error) {
        const errorMessage = extractErrorMessage(error)
        toast.close()
        toast.error(errorMessage || '删除配置失败')
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 检查是否是七牛云S3兼容模式
const isQiniuMode = (platform: PlatformConfig): boolean => {
  return (
    platform.value === 's3' &&
    platform.form.endpoint &&
    platform.form.endpoint.toLowerCase().includes('qiniucs.com')
  )
}

// 清空表单数据
const clearFormData = (form: StorageForm) => {
  Object.keys(form).forEach((key) => {
    form[key] = ''
  })
}

// 获取自定义域名输入框的提示文本
const getDomainPlaceholder = (platform: PlatformConfig) => {
  // 如果是七牛云S3兼容模式，显示必填提示
  if (isQiniuMode(platform)) {
    return '七牛云S3兼容模式下，自定义域名必填'
  }

  // 如果平台配置中指定了必填，显示必填提示
  if (platform.customDomainRequired) {
    return '请输入自定义域名'
  }

  // 否则显示可选提示
  return '请输入自定义域名（可选）'
}

// 显示字段帮助信息
const showFieldHelp = (field: string, platform: PlatformConfig) => {
  let helpText = ''

  // 根据字段类型和平台类型显示不同的帮助信息
  switch (field) {
    case 'bucketName':
      helpText = '存储桶名称，请按照平台要求填写'
      break
    case 'region':
      // 根据不同平台提供特定的 region 帮助信息
      if (platform.value === 'oss') {
        helpText = '阿里云 OSS 地域节点，例如：cn-hangzhou'
      } else if (platform.value === 'cos') {
        helpText = '腾讯云 COS 地域节点，例如：ap-shanghai'
      } else if (platform.value === 's3') {
        helpText = 'S3 地域节点，例如：ap-northeast-1'
      } else if (platform.value === 'r2') {
        helpText = 'Cloudflare R2 没有 region 概念，此处填写的是 accountId'
      } else if (platform.value === 'minio') {
        helpText = 'MinIO 通常不需要 region 参数'
      } else {
        helpText = platform.tips || '地域节点，请按照平台要求填写'
      }
      break
    case 'endpoint':
      // 根据不同平台提供特定的 endpoint 帮助信息
      if (platform.value === 'oss') {
        helpText = '阿里云 OSS 端点地址，例如：https://oss-cn-hangzhou.aliyuncs.com'
      } else if (platform.value === 'cos') {
        helpText = '腾讯云 COS 端点地址，例如：https://cos.ap-shanghai.myqcloud.com'
      } else if (platform.value === 's3') {
        helpText = 'S3 端点地址，例如：https://s3.ap-northeast-1.amazonaws.com'
      } else if (platform.value === 'r2') {
        helpText = 'Cloudflare R2 端点地址，例如：https://<accountid>.r2.cloudflarestorage.com'
      } else if (platform.value === 'minio') {
        helpText = 'MinIO 端点地址，例如：http://*************:9000'
      } else {
        helpText = '端点地址，请按照平台要求填写'
      }
      break
    case 'accountId':
      helpText =
        'Cloudflare R2 的账户ID，形如：1991125856666c8f18ac439df183cfaa，其他平台不需要填写'
      break
    case 'accessKey':
      helpText = '访问密钥，请从平台获取'
      break
    case 'accessSecret':
      helpText = '访问密钥密码，请从平台获取'
      break
    case 'customDomain':
      if (isQiniuMode(platform)) {
        helpText = '七牛云S3兼容模式下，自定义域名必填'
      } else if (platform.value === 'r2') {
        helpText = 'Cloudflare R2 必须填写自定义域名'
      } else if (platform.customDomainRequired) {
        helpText = '自定义域名，必填项'
      } else if (platform.value === 'oss') {
        helpText = '阿里云 OSS 自定义域名，可选项'
      } else if (platform.value === 'cos') {
        helpText = '腾讯云 COS 自定义域名，可选项'
      } else if (platform.value === 's3') {
        helpText = 'S3 自定义域名，可选项'
      } else if (platform.value === 'minio') {
        helpText = 'MinIO 自定义域名，可选项'
      } else {
        helpText = '自定义域名，可选项'
      }
      break
    default:
      helpText = '请填写完整的配置信息'
  }

  // 使用 wot-design-uni 的 message 组件显示帮助信息
  message.alert({
    title: '帮助信息',
    msg: helpText,
    confirmButtonText: '知道了',
  })
}
</script>

<style lang="scss" scoped>
.enpower-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  min-height: 100vh;
  background-color: #f5f7fa;
}

:deep(.wd-card) {
  width: 690rpx;
  padding: 0px !important;
  overflow: hidden;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
/* 平台选择器样式 */
.platform-selector {
  overflow: hidden;
  background-color: #fff;
  border-radius: 16rpx;
}

:deep(.wd-select-picker__label) {
  font-size: 16px;
  font-weight: 600;
  color: #3a3a3a;
}

:deep(.wd-select-picker__value) {
  font-weight: 500;
  color: #337ea9;
}

.form-footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: 32rpx;
}

:deep(.wd-input__label) {
  font-weight: 400;
}

:deep(.wd-cell-group__body) {
  overflow: hidden;
  border-radius: 12rpx;
}

:deep(.form-item) {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

:deep(.wd-card__title-content) {
  padding: 6px 16px !important;
}

.settings-group {
  padding: 0 16px;
}

.form-item-container {
  position: relative;
  margin-bottom: 32rpx;
  border-bottom: 2px solid #e0e0e0;
}

.form-label {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 4rpx;
  margin-bottom: 16rpx;
  font-size: 15px;
  font-weight: 500;
  color: #333;
  letter-spacing: 0.5px;
}

.help-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
  cursor: pointer;
}

.form-input {
  width: 100%;
  padding: 8rpx 0;
  margin-bottom: 8rpx;
}

.form-item-container::after {
  position: absolute;
  bottom: -2px;
  /* 覆盖底部边框 */
  left: 0;
  z-index: 1;
  width: 100%;
  height: 2px;
  content: '';
  background: linear-gradient(to right, #337ea9, #4a90e2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.form-item-container:hover::after {
  opacity: 1;
}
/* 聚焦状态下的样式 */
:deep(.wd-input.is-focused) + .form-item-container-focus-indicator {
  position: absolute;
  bottom: -2px;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, #337ea9, #4a90e2);
}

:deep(.wd-button) {
  height: 88rpx;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.wd-button--primary) {
  background-color: #337ea9;
  border-color: #337ea9;
}

:deep(.wd-button--primary:active) {
  background-color: #2a6a8f;
  border-color: #2a6a8f;
  transform: translateY(2rpx);
}

:deep(.wd-button--info) {
  color: #666;
  background-color: #f5f7fa;
  border-color: #ddd;
}

:deep(.wd-button--info:active) {
  color: #555;
  background-color: #e8e8e8;
  border-color: #ccc;
  transform: translateY(2rpx);
}
</style>
