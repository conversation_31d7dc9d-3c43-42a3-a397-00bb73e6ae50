<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '图床配置',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="cloudpic-container">
    <!-- 顶部标题 -->
    <view class="header">
      <wd-text size="20px" text="选择图床配置方式" color="#333" bold custom-class="text-center" />
      <wd-text
        size="14px"
        text="请根据您的需求选择合适的图床配置方式"
        color="#666"
        custom-class="text-center mt-2"
      />
    </view>

    <!-- 选项卡片 -->
    <view class="options-container">
      <!-- Cloudinary 选项卡片 -->
      <view class="option-card" @click="handleCloudinary">
        <view class="option-icon-container cloudinary-bg">
          <view class="i-solar:cloud-upload-outline w-[40px] h-[40px] c-white"></view>
        </view>
        <view class="option-content">
          <view class="option-title">
            <wd-text size="18px" text="Cloudinary 图床" color="#333" bold />
          </view>
          <view class="option-description">
            <wd-text size="14px" text="适合初次使用图床的用户" color="#666" />
          </view>
          <view class="option-features">
            <view class="feature-item">
              <view class="i-solar:check-circle-bold w-[16px] h-[16px] c-#337ea9"></view>
              <wd-text size="13px" text="简单易用，配置步骤少" color="#666" />
            </view>
            <view class="feature-item">
              <view class="i-solar:check-circle-bold w-[16px] h-[16px] c-#337ea9"></view>
              <wd-text size="13px" text="免费额度足够日常使用" color="#666" />
            </view>
            <view class="feature-item">
              <view class="i-solar:check-circle-bold w-[16px] h-[16px] c-#337ea9"></view>
              <wd-text size="13px" text="无需其他技术基础" color="#666" />
            </view>
            <view class="feature-item">
              <view class="i-solar:check-circle-bold w-[16px] h-[16px] c-#337ea9"></view>
              <wd-text size="13px" text="仅支持保存图片类型" color="#666" />
            </view>
          </view>
        </view>
        <view class="option-arrow">
          <view class="i-solar:arrow-right-outline w-[24px] h-[24px] c-#999"></view>
        </view>
      </view>

      <!-- 专业图床选项卡片 -->
      <view class="option-card" @click="handleProfessional">
        <view class="option-icon-container professional-bg">
          <view class="i-solar:server-bold w-[40px] h-[40px] c-white"></view>
        </view>
        <view class="option-content">
          <view class="option-title">
            <wd-text size="18px" text="更多专业图床配置" color="#333" bold />
          </view>
          <view class="option-description">
            <wd-text size="14px" text="适合有过图床使用配置经验的用户" color="#666" />
          </view>
          <view class="option-features">
            <view class="feature-item">
              <view class="i-solar:check-circle-bold w-[16px] h-[16px] c-#337ea9"></view>
              <wd-text size="13px" text="阿里云|腾讯云|七牛云|S3兼容图床" color="#666" />
            </view>
            <view class="feature-item">
              <view class="i-solar:check-circle-bold w-[16px] h-[16px] c-#337ea9"></view>
              <wd-text size="13px" text="稳定|高可用|大多数需要付费使用" color="#666" />
            </view>
            <view class="feature-item">
              <view class="i-solar:check-circle-bold w-[16px] h-[16px] c-#337ea9"></view>
              <wd-text size="13px" text="支持更多文件类型" color="#666" />
            </view>
            <view class="feature-item">
              <view class="i-solar:check-circle-bold w-[16px] h-[16px] c-#337ea9"></view>
              <wd-text size="13px" text="收藏助手的增强功能必须配这个" color="#666" />
            </view>
          </view>
        </view>
        <view class="option-arrow">
          <view class="i-solar:arrow-right-outline w-[24px] h-[24px] c-#999"></view>
        </view>
      </view>
      <wd-text size="13px" text="如果不确定选择哪种方式，建议选择 Cloudinary" color="#999" />
    </view>
  </view>
</template>

<script lang="ts" setup>
// 跳转到 Cloudinary 配置页面
const handleCloudinary = () => {
  uni.navigateTo({
    url: '/pages/about/cloudpic/cloudinary/cloudinary',
  })
}

// 跳转到专业图床配置页面
const handleProfessional = () => {
  uni.navigateTo({
    url: '/pages/about/cloudpic/enpower/enpower',
  })
}
</script>

<style lang="scss" scoped>
.cloudpic-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 32rpx;
  background-color: #f8f9fa;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-items: start;
  margin-bottom: 40rpx;
}

.option-card {
  position: relative;
  display: flex;
  padding: 24rpx;
  margin-bottom: 32rpx;
  cursor: pointer;
  background-color: #fff;
  border: 2px solid transparent;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.option-card:hover {
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
  transform: translateY(-2rpx);
}

.option-card:active {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transform: translateY(0);
}

.option-icon-container {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
  border-radius: 16rpx;
}

.cloudinary-bg {
  background: linear-gradient(135deg, #337ea9 0%, #2a6a8f 100%);
}

.professional-bg {
  background: linear-gradient(135deg, #5d4dc4 0%, #8066ff 100%);
}

.option-content {
  flex: 1;
}

.option-title {
  margin-bottom: 8rpx;
}

.option-description {
  margin-bottom: 16rpx;
}

.option-features {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.feature-item {
  display: flex;
  gap: 8rpx;
  align-items: center;
}

.option-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  margin-left: 16rpx;
}

.text-center {
  text-align: center;
}

.mt-2 {
  margin-top: 8rpx;
}
</style>
