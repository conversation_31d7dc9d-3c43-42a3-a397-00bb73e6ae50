<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'Cloudinary配置',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view
    class="overflow-hidden flex flex-col items-center justify-start w-full h-100% m-0 bg-#f5f7fa"
  >
    <view class="cloudinary-container">
      <wd-form :model="formData" :rules="rules" ref="formRef" label-align="top">
        <wd-card class="tr-shadow rounded-12px !mx-0">
          <view class="settings-group">
            <!-- Cloud Name 输入框 -->
            <view class="form-item-container">
              <view class="form-label">Cloud name</view>
              <wd-input
                no-border
                prop="cloudName"
                clearable
                v-model="formData.cloudName"
                placeholder="请输入 Cloud name"
                custom-class="form-input"
              />
              <view class="form-item-container-focus-indicator"></view>
            </view>

            <!-- API Key 输入框 -->
            <view class="form-item-container">
              <view class="form-label">API Key</view>
              <wd-input
                no-border
                prop="apiKey"
                clearable
                v-model="formData.apiKey"
                placeholder="请输入 API Key"
                custom-class="form-input"
              />
              <view class="form-item-container-focus-indicator"></view>
            </view>

            <!-- API Secret 输入框 -->
            <view class="form-item-container">
              <view class="form-label">API Secret</view>
              <wd-input
                no-border
                prop="apiSecret"
                show-password
                clearable
                v-model="formData.apiSecret"
                placeholder="请输入 API Secret"
                custom-class="form-input"
              />
              <view class="form-item-container-focus-indicator"></view>
            </view>
          </view>
        </wd-card>
        <view class="form-footer">
          <wd-button
            type="info"
            @click="handleRemove"
            custom-class="tr-shadow btn-delete"
            :disabled="!hasLoadedConfig"
          >
            删除配置
          </wd-button>
          <wd-button
            type="primary"
            @click="handleSubmit"
            custom-class="tr-shadow btn-save"
            :disabled="!isFormComplete"
          >
            保存配置
          </wd-button>
        </view>
      </wd-form>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { useToast, useMessage } from 'wot-design-uni'
import { onShow } from '@dcloudio/uni-app'
import {
  getCloudinaryConfig,
  postCloudinaryConfig,
  Cloudinary,
  removeCloudinaryConfig,
} from '@/service/piccloud/cloudinary'
import { extractErrorMessage } from '@/utils'

const toast = useToast()
const message = useMessage()
const formRef = ref()
const formData = reactive({
  cloudName: '',
  apiKey: '',
  apiSecret: '',
})

const rules = {
  cloudName: [{ required: true, message: '请输入Cloud Name' }],
  apiKey: [{ required: true, message: '请输入API Key' }],
  apiSecret: [{ required: true, message: '请输入API Secret' }],
}

// 是否成功加载到用户图床数据
const hasLoadedConfig = ref(false)

// 计算属性：检查表单是否完整（三个字段都不为空）
const isFormComplete = computed(() => {
  return !!formData.cloudName && !!formData.apiKey && !!formData.apiSecret
})

// 获取已保存的配置
const fetchCloudinaryConfig = async () => {
  try {
    toast.loading('加载配置中...')
    const res = await getCloudinaryConfig()
    toast.close()
    if (res.success && res.data) {
      const config = res.data
      formData.cloudName = config.cloudName || ''
      formData.apiKey = config.apiKey || ''
      formData.apiSecret = config.apiSecret || ''

      // 如果成功加载到数据，设置标志为 true
      if (formData.cloudName && formData.apiKey && formData.apiSecret) {
        hasLoadedConfig.value = true
      }
    }
  } catch (error) {
    toast.close()
    const errorMessage = extractErrorMessage(error)
    toast.error(errorMessage || '获取配置失败，请稍后重试')
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 检查是否有已保存的配置
onShow(() => {
  fetchCloudinaryConfig()
})

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    toast.loading('保存配置中...')
    const configData: Cloudinary = {
      cloudName: formData.cloudName,
      apiKey: formData.apiKey,
      apiSecret: formData.apiSecret,
    }

    const res = await postCloudinaryConfig(configData)
    console.log('保存配置结果:', res)
    // 先关闭加载提示
    toast.close()

    if (res.success) {
      // 保存成功后更新加载状态
      hasLoadedConfig.value = true
      // 显示成功提示
      toast.success('配置保存成功')
    } else {
      toast.error(res.msg || '保存失败，请稍后重试')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    toast.close() // 关闭加载提示
    toast.error(errorMessage || '表单验证失败，请检查输入')
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

const handleRemove = () => {
  // 显示确认对话框
  message
    .confirm({
      title: '确认删除',
      msg: '确定要删除当前配置吗？',
    })
    .then(async () => {
      try {
        toast.loading('删除配置中...')

        const res = await removeCloudinaryConfig()
        toast.close()
        if (res.success) {
          // 清空表单
          formData.cloudName = ''
          formData.apiKey = ''
          formData.apiSecret = ''
          // 重置加载状态
          hasLoadedConfig.value = false
          toast.success('配置已删除')
        } else {
          toast.error(res.msg || '删除失败，请稍后重试')
        }
      } catch (error) {
        toast.close()
        const errorMessage = extractErrorMessage(error)
        toast.error(errorMessage || '删除配置失败，请稍后重试')
      } finally {
        // 确保在所有情况下都关闭加载提示
        setTimeout(() => {
          toast.close()
        }, 500)
      }
    })
    .catch(() => {
      // 用户取消删除，不做任何处理
    })
}
</script>

<style lang="scss" scoped>
.cloudinary-container {
  width: 100%;
  min-height: 100vh;
  margin-top: 12px;
  background-color: #f5f7fa;
}

.card-title {
  padding-bottom: 16rpx;
  margin-bottom: 32rpx;
  font-size: 20px;
  font-weight: 600;
  color: #222;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #eee;
}

.settings-group {
  padding: 0 16rpx;
}

.form-item-container {
  margin-bottom: 24rpx;
}

.form-label {
  position: relative;
  display: inline-block;
  margin-bottom: 12rpx;
  font-size: 16px;
  font-weight: 500;
  color: #444;
}

.form-input {
  width: 100%;
  padding: 8rpx 0;
  margin-bottom: 8rpx;
}

.form-item-container {
  position: relative;
  margin-bottom: 32rpx;
  border-bottom: 2px solid #e0e0e0;
}

.form-item-container::after {
  position: absolute;
  bottom: -2px;
  /* 覆盖底部边框 */
  left: 0;
  z-index: 1;
  width: 100%;
  height: 2px;
  content: '';
  background: linear-gradient(to right, #337ea9, #4a90e2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.form-item-container:hover::after {
  opacity: 1;
}

// 聚焦状态下的样式
:deep(.wd-input.is-focused) + .form-item-container-focus-indicator {
  position: absolute;
  bottom: -2px;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, #337ea9, #4a90e2);
}

.form-tips {
  display: flex;
  align-items: center;
  padding: 0 8rpx;
  margin-top: 32rpx;
  margin-bottom: 16rpx;
  font-size: 13px;
  // font-style: italic;
  line-height: 1.5;
  color: #888;
}

// .form-tips::before {
//   margin-right: 8rpx;
//   font-size: 16px;
//   content: '\1F4A1';
//   /* 灯泡表情符号 */
// }

.form-footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: 60rpx;
}

:deep(.btn-save) {
  width: 40% !important;
  margin: auto;
}

:deep(.btn-delete) {
  width: 40% !important;
  margin: auto;
}

:deep(.wd-button) {
  height: 88rpx;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.wd-button--primary) {
  background-color: #337ea9;
  border-color: #337ea9;
}

:deep(.wd-button--primary:active) {
  background-color: #2a6a8f;
  border-color: #2a6a8f;
  transform: translateY(2rpx);
}

:deep(.wd-button--info) {
  color: #666;
  background-color: #f5f7fa;
  border-color: #ddd;
}

:deep(.wd-button--info:active) {
  color: #555;
  background-color: #e8e8e8;
  border-color: #ccc;
  transform: translateY(2rpx);
}

:deep(.wd-button.is-disabled) {
  cursor: not-allowed;
  opacity: 0.5;
}

:deep(.wd-button.is-disabled:active) {
  transform: none;
}

:deep(.wd-input) {
  width: 100% !important;
}

:deep(.wd-input__inner) {
  display: block !important;
  width: 100% !important;
  height: 80rpx !important;
  padding-left: 4rpx !important;
  font-size: 15px !important;
  font-weight: 400 !important;
  color: #333 !important;
  background-color: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  transition: all 0.3s ease !important;
}

:deep(.wd-input__inner:focus),
:deep(.wd-input.is-focused .wd-input__inner) {
  box-shadow: none !important;
}

:deep(.wd-input__wrapper) {
  width: 100% !important;
  padding: 0 !important;
  background-color: transparent !important;
}

:deep(.wd-cell) {
  width: 100% !important;
}

:deep(.wd-input__label) {
  font-size: 14px !important;
  color: #333 !important;
}

:deep(.wd-input__prefix),
:deep(.wd-input__suffix) {
  color: #999;
}

:deep(.wd-input__clear) {
  font-size: 16px;
  color: #999;
}

:deep(.wd-card) {
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

:deep(.wd-form) {
  margin-right: 0 !important;
  margin-left: 0 !important;
}
</style>
