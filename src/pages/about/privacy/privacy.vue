<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '隐私声明',
    navigationBarBackgroundColor: '#fff',
  },
}
</route>

<template>
  <view class="privacy-container p-4">
    <view class="privacy-content">
      <view class="mb-6">
        <view class="text-xl font-bold mb-4">隐私声明</view>
        <view class="text-gray-600 text-sm leading-6 mb-4">
          感谢您使用我们的服务。我们非常重视您的隐私保护，并致力于保护您的个人信息安全。本隐私声明旨在向您说明我们如何收集、使用和保护您的个人信息。
        </view>
      </view>

      <view class="section mb-6">
        <view class="text-lg font-bold mb-3">1. 信息收集</view>
        <view class="text-gray-600 text-sm leading-6 mb-2">我们可能收集以下类型的信息：</view>
        <view class="ml-4">
          <view class="text-gray-600 text-sm leading-6">基本信息：包括您的用户名、头像、邮箱</view>
          <view class="text-gray-600 text-sm leading-6">使用数据：剪藏次数统计、报错日志</view>
          <view class="text-gray-600 text-sm leading-6">
            <text style="font-weight: bold">特别说明</text>
            ：本小程序仅获取功能所需的授权能力，除授权需要读取用户数据库结构信息外，无任何需要读取用户
            Notion 数据的功能
          </view>
          <view class="text-gray-600 text-sm leading-6">
            <text style="font-weight: bold">特别强调</text>
            ：本小程序不存储用户剪藏数据，剪藏的数据存储在用户的 Notion
            数据库中，剪藏的图片存储在用户自己的图床中。
          </view>
        </view>
      </view>

      <view class="section mb-6">
        <view class="text-lg font-bold mb-3">2. 信息使用</view>
        <view class="text-gray-600 text-sm leading-6 mb-2">我们收集的信息将用于：</view>
        <view class="ml-4">
          <view class="text-gray-600 text-sm leading-6">• 提供和改进我们的服务</view>
          <view class="text-gray-600 text-sm leading-6">• 个性化您的使用体验</view>
          <view class="text-gray-600 text-sm leading-6">• 发送服务通知和更新</view>
        </view>
      </view>

      <view class="section mb-6">
        <view class="text-lg font-bold mb-3">3. 信息保护</view>
        <view class="text-gray-600 text-sm leading-6">
          采用业界标准的安全措施保护您的个人信息，包括数据加密、访问控制等技术手段，防止您的信息遭到未经授权的访问、使用或泄露。
        </view>
      </view>

      <view class="section mb-6">
        <view class="text-lg font-bold mb-3">4. 信息共享</view>
        <view class="text-gray-600 text-sm leading-6">不共享。</view>
      </view>

      <view class="section">
        <view class="text-lg font-bold mb-3">5. 隐私声明更新</view>
        <view class="text-gray-600 text-sm leading-6">
          我们可能会不时更新本隐私声明。更新后的声明将在本页面上发布，并注明更新日期。建议您定期查看本页面以了解最新的隐私保护政策。
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
//
</script>

<style lang="scss" scoped>
.privacy-container {
  min-height: 100vh;
  background-color: #f8f8f7;
}

.privacy-content {
  max-width: 800px;
  margin: 0 auto;
}
</style>
