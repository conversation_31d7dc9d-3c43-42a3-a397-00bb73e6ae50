<route lang="json5">
{
  style: {
    navigationBarTitleText: 'NotionMpClipper',
    navigationBarBackgroundColor: '#337ea9',
    navigationBarTextStyle: 'white',
  },
}
</route>

<template>
  <view class="flex flex-col items-center justify-start w-full h-100% m-0 bg-#f8f9fa">
    <wd-toast />
    <!-- 顶部区域：使用渐变背景 -->
    <view class="flex flex-col items-center justify-center mt-0 w-full h-50% header-bg pt-5">
      <!-- Notion官方图片，添加轻微阴影效果 -->
      <wd-img
        width="300rpx"
        model="aspectFit"
        height="485rpx"
        custom-class="logo"
        src="/static/people2.png"
      />
      <!-- <wd-text text="NotionMpClipper" size="24px" custom-class="c-white font-bold mt-50rpx app-title"></wd-text> -->
      <wd-gap height="50rpx"></wd-gap>
    </view>

    <!-- 内容区域：使用卡片式设计 -->
    <view class="content-card">
      <wd-text
        text="微信公众号剪藏助手"
        size="22px"
        custom-class="!c-#337ea9 font-bold mb-4"
      ></wd-text>
      <wd-text
        text="这是一款专为Notion打造的微信小程序。"
        size="14px"
        custom-class="!c-#333 mb-3 text-center"
      ></wd-text>
      <wd-text
        text="这款小程序能够让用户在微信中绑定他们的Notion账号，创建个性化的模板，而后将感兴趣的公众号文章快速转发至小程序，实现与预先设置的Notion页面的同步。"
        size="13px"
        custom-class="!c-#666 text-center"
      ></wd-text>
    </view>

    <!-- 登录按钮：使用主题色和明确文本 -->
    <view class="login-btn-container" @click="handleLogin">
      <button class="login-btn" hover-class="login-btn-hover">
        <text class="login-text">Home</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/store'
import { wxLoginApi } from '@/service/user/auth'
import { getUserInfo } from '@/service/user/user'
import { DEFAULT_AVATAR } from '@/constants/common'
import { useToast } from 'wot-design-uni'
import { extractErrorMessage } from '@/utils'

// 获取重定向地址
const redirect = ref('')

// 初始化toast
const toast = useToast()

// 在页面加载时获取重定向地址
onLoad((options) => {
  if (options.redirect) {
    redirect.value = decodeURIComponent(options.redirect)
  }
})

// 处理登录
const handleLogin = async () => {
  try {
    toast.loading('登录中...')

    // 获取微信登录凭证
    const { code } = await uni.login({ provider: 'weixin' })

    // 调用登录API
    const { data } = await wxLoginApi(code)

    // 获取用户存储
    const userStore = useUserStore()

    // 处理登录结果
    if (data) {
      if (typeof data === 'object') {
        // 如果返回的是用户信息对象
        userStore.setUserInfo(data)
      } else if (typeof data === 'string') {
        // 如果只返回了token
        userStore.setToken(data)

        // 异步获取用户详细信息
        fetchUserInfo()
      }

      // 登录成功提示
      toast.success('登录成功')

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        // 如果有重定向地址，则跳转到该地址
        if (redirect.value) {
          uni.redirectTo({
            url: redirect.value,
            fail: () => {
              // 如果跳转失败，可能是tabBar页面，尝试switchTab
              uni.switchTab({
                url: redirect.value,
                fail: () => {
                  // 如果还是失败，跳转到首页
                  uni.switchTab({ url: '/pages/index/index' })
                },
              })
            },
          })
        } else {
          // 没有重定向地址，跳转到首页
          uni.switchTab({ url: '/pages/index/index' })
        }
      }, 1500)
    } else {
      toast.error('登录失败，请重试')
    }
  } catch (error) {
    const errorMessage = extractErrorMessage(error)
    console.error('登录失败:', errorMessage)
    toast.error(errorMessage || '登录失败，请重试')
  } finally {
    // 确保在所有情况下都关闭加载提示
    setTimeout(() => {
      toast.close()
    }, 500)
  }
}

// 获取用户详细信息
const fetchUserInfo = async () => {
  const userStore = useUserStore()

  try {
    console.log('开始获取用户详细信息')
    const { data } = await getUserInfo()

    if (data) {
      // 合并新获取的用户信息与现有信息
      const mergedUserInfo = { ...userStore.userInfo, ...data }
      if (!mergedUserInfo.avatar || mergedUserInfo.avatar.startsWith('wxfile')) {
        mergedUserInfo.avatar = DEFAULT_AVATAR
      }
      userStore.setUserInfo(mergedUserInfo)
      console.log('用户详细信息已更新:', mergedUserInfo)
    } else {
      console.warn('获取用户详细信息失败: 返回数据为空')
    }
  } catch (error) {
    console.error('获取用户详细信息失败:', error)
  }
}
</script>

<style lang="scss" scoped>
@keyframes floatAnimation {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10rpx);
  }

  100% {
    transform: translateY(0);
  }
}

page {
  height: 100%;
  background-color: #f8f9fa;
}

// 顶部渐变背景
.header-bg {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #337ea9 0%, #2c6a8f 100%);

  &::after {
    position: absolute;
    right: 0;
    bottom: -20rpx;
    left: 0;
    height: 40rpx;
    content: '';
    background: #f8f9fa;
    border-radius: 50% 50% 0 0 / 100% 100% 0 0;
  }
}

// 应用标题样式
.app-title {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: 1px;
}

// Logo样式优化
.logo {
  width: 300rpx;
  background-color: #f8f9fa;
  border-radius: 50%;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
  animation: floatAnimation 3s ease-in-out infinite;
}

// 内容卡片样式
.content-card {
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 85%;
  padding: 30rpx;
  margin-top: -20rpx;
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

// 登录按钮容器
.login-btn-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 60rpx;
}

// 登录按钮样式
.login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300rpx;
  height: 90rpx;
  color: white;
  background: linear-gradient(135deg, #337ea9 0%, #2c6a8f 100%);
  border: none;
  border-radius: 45rpx;
  box-shadow: 0 4px 12px rgba(51, 126, 169, 0.3);
  transition: all 0.3s ease;
}

// 登录按钮悬浮效果
.login-btn-hover {
  box-shadow: 0 6px 16px rgba(51, 126, 169, 0.4);
  transform: translateY(-3rpx) scale(1.02);
}

// 登录按钮文本
.login-text {
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 1px;
}

// 深度选择器优化logo样式
:deep(.logo) {
  width: 300rpx;
  background-color: #f8f9fa;
  border-radius: 50%;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  animation: floatAnimation 3s ease-in-out infinite;
}
</style>
