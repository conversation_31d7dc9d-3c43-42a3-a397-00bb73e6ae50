import { defineStore } from 'pinia'
import {
  getNotionArticleDatabaseTagOptionsAPI,
  syncUserCommonTagsAPI,
  deleteUserCommonTagsAPI,
} from '@/service/notion/tagsync'
import type { NotionTagFieldResponse } from '@/service/notion/tagsync'

// 标签存储，使用LRU队列管理历史标签
export const useTagStore = defineStore('tag', {
  state: () => ({
    // 历史标签队列，最多保存30个
    historyTags: [] as string[],
    // 队列最大容量
    maxCapacity: 30,
    // 同步状态
    syncLoading: false,
    // 备份状态
    backupLoading: false,
    // 删除备份状态
    deleteLoading: false,
  }),

  actions: {
    // 初始化标签，从本地存储加载
    initTags() {
      try {
        const storedTags = uni.getStorageSync('tags')
        if (storedTags) {
          this.historyTags = JSON.parse(storedTags)
        }
      } catch (e) {
        console.error('加载历史标签失败:', e)
        this.historyTags = []
      }
    },

    // 保存标签到本地存储
    saveTags() {
      try {
        uni.setStorageSync('tags', JSON.stringify(this.historyTags))
      } catch (e) {
        console.error('保存历史标签失败:', e)
      }
    },

    // 添加新标签到队列最前面
    addTag(tag: string) {
      // 如果标签已存在，先移除
      this.removeTag(tag)

      // 添加到队列最前面
      this.historyTags.unshift(tag)

      // 如果超出容量，移除末尾元素
      if (this.historyTags.length > this.maxCapacity) {
        this.historyTags.pop()
      }

      // 保存到本地存储
      this.saveTags()
    },

    // 使用标签（选择已有标签），将其移到队列最前面
    useTag(tag: string) {
      // 如果标签不存在，直接返回
      const index = this.historyTags.indexOf(tag)
      if (index === -1) return

      // 从当前位置移除
      this.historyTags.splice(index, 1)

      // 添加到队列最前面
      this.historyTags.unshift(tag)

      // 保存到本地存储
      this.saveTags()
    },

    // 从队列中移除指定标签
    removeTag(tag: string) {
      const index = this.historyTags.indexOf(tag)
      if (index !== -1) {
        this.historyTags.splice(index, 1)
        this.saveTags()
      }
    },

    // 根据索引删除标签
    removeTagByIndex(index: number) {
      if (index >= 0 && index < this.historyTags.length) {
        this.historyTags.splice(index, 1)
        this.saveTags()
      }
    },

    // 获取所有历史标签
    getAllTags() {
      return this.historyTags
    },

    // 清空所有历史标签
    clearAllTags() {
      this.historyTags = []
      this.saveTags()
    },

    // 从 Notion 同步标签到本地（合并模式，本地标签排前面）
    async syncTagsFromNotion() {
      this.syncLoading = true
      try {
        console.log('开始从 Notion 同步标签')
        const response = await getNotionArticleDatabaseTagOptionsAPI()

        if (response.code === 200 && response.data) {
          // 从接口返回的 options 字段获取标签数组
          const notionTags = response.data.options
          console.log('从 Notion 获取到标签:', notionTags.length, '个')

          // 合并标签：本地标签在前，Notion标签在后，去重
          const mergedTags = [...this.historyTags]

          // 添加 Notion 标签，但要避免重复
          notionTags.forEach((tag) => {
            if (!mergedTags.includes(tag)) {
              mergedTags.push(tag)
            }
          })

          // 限制总数量不超过最大容量
          if (mergedTags.length > this.maxCapacity) {
            // 保留本地标签，截取部分 Notion 标签
            const localTagsCount = this.historyTags.length
            const maxNotionTags = this.maxCapacity - localTagsCount
            this.historyTags = [
              ...this.historyTags,
              ...notionTags
                .filter((tag) => !this.historyTags.includes(tag))
                .slice(0, maxNotionTags),
            ]
          } else {
            this.historyTags = mergedTags
          }

          // 保存到本地存储
          this.saveTags()

          console.log('标签同步完成，当前标签数量:', this.historyTags.length)

          return {
            success: true,
            syncedCount: notionTags.length,
            totalCount: this.historyTags.length,
          }
        } else {
          throw new Error(response.msg || '同步失败')
        }
      } catch (error) {
        console.error('从 Notion 同步标签失败:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
        }
      } finally {
        this.syncLoading = false
      }
    },

    // 备份本地标签到服务器
    async backupTagsToServer() {
      this.backupLoading = true
      try {
        console.log('开始备份本地标签到服务器')
        const response = await syncUserCommonTagsAPI(this.historyTags)

        console.log('备份接口响应:', response)
        console.log('响应数据 (boolean):', response.data)

        if (response.code === 200 && response.data === true) {
          const backupCount = this.historyTags.length
          console.log('标签备份完成，备份数量:', backupCount)

          return {
            success: true,
            backupCount,
          }
        } else {
          throw new Error(response.msg || '备份失败')
        }
      } catch (error) {
        console.error('备份标签到服务器失败:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
        }
      } finally {
        this.backupLoading = false
      }
    },

    // 删除服务器上的标签备份
    async deleteTagsFromServer() {
      this.deleteLoading = true
      try {
        console.log('开始删除服务器上的标签备份')
        const response = await deleteUserCommonTagsAPI()

        console.log('删除备份接口响应:', response)
        console.log('响应数据 (boolean):', response.data)

        if (response.code === 200 && response.data === true) {
          console.log('标签备份删除完成')

          return {
            success: true,
          }
        } else {
          throw new Error(response.msg || '删除备份失败')
        }
      } catch (error) {
        console.error('删除服务器标签备份失败:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
        }
      } finally {
        this.deleteLoading = false
      }
    },
  },
})
