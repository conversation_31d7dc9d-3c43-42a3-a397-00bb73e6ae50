<template>
  <GlobalLoading :show="userStore.isAppInitializing || userStore.isLoginLoading" />
</template>

<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useUserStore } from '@/store'
import { wxLoginApi, checkSessionApi } from '@/service/user/auth'
import { getUserInfo } from '@/service/user/user'
import { DEFAULT_AVATAR } from '@/constants/common'
import type { IUserInfo } from '@/typings'
import GlobalLoading from '@/components/global-loading/global-loading.vue'

const userStore = useUserStore()

onLaunch(async () => {
  console.log('App Launch')

  // 立即设置应用初始化完成，让页面先渲染
  const userStore = useUserStore()

  // 异步执行登录检查，不阻塞页面渲染
  setTimeout(() => {
    checkLoginStatusAsync()
  }, 100)

  // 标记应用初始化完成，让页面可以渲染
  userStore.setAppInitializing(false)
})

onShow(() => {
  console.log('App Show')
  // 检查是否设置了快捷剪藏作为入口页面
  const options = uni.getEnterOptionsSync()
  console.log('App Show - enterOptions:', options)
  if (options.scene !== 1173) {
    checkDefaultPage()
  }
})

onHide(() => {
  console.log('App Hide')
})

// 异步检查登录状态（不阻塞页面渲染）
const checkLoginStatusAsync = async () => {
  const userStore = useUserStore()

  try {
    userStore.setLoginLoading(true)
    console.log('开始异步检查登录状态')

    // 添加超时处理，避免无限等待
    const loginPromise = checkLoginStatus()
    const timeoutPromise = new Promise((resolve, reject) => {
      setTimeout(() => reject(new Error('登录超时')), 10000) // 10秒超时
    })

    await Promise.race([loginPromise, timeoutPromise])
    console.log('登录状态检查完成')
  } catch (error) {
    console.error('登录状态检查失败:', error)
    // 登录失败也不影响页面使用，只是某些功能可能受限
  } finally {
    userStore.setLoginLoading(false)
  }
}

// 检查登录状态
const checkLoginStatus = async () => {
  const userStore = useUserStore()

  try {
    // 检查当前登录状态是否有效
    if (userStore.isLogined) {
      console.log('检查登录会话是否有效')
      console.log('当前 token:', userStore.userInfo.token)

      try {
        const { data: isSessionValid } = await checkSessionApi()
        console.log('会话检查结果:', isSessionValid)

        if (isSessionValid) {
          console.log('登录会话有效，无需重新登录')
          // 会话有效，异步获取用户详细信息
          fetchUserInfo()
          return
        }
        console.log('登录会话已过期，需要重新登录')
      } catch (sessionError) {
        console.error('会话检查失败:', sessionError)
        console.log('将尝试重新登录')
      }
    } else {
      console.log('用户未登录，需要执行登录流程')
    }

    // 会话无效或未登录，执行静默登录
    await doLogin()
  } catch (error) {
    console.error('检查登录状态失败:', error)
    throw error // 重新抛出错误，让上层处理
  }
}

// 获取用户详细信息
const fetchUserInfo = async () => {
  const userStore = useUserStore()

  try {
    console.log('开始获取用户详细信息')
    const { data } = await getUserInfo()

    if (data) {
      // 合并新获取的用户信息与现有信息
      const mergedUserInfo = { ...userStore.userInfo, ...data }
      if (!mergedUserInfo.avatar || mergedUserInfo.avatar.startsWith('wxfile')) {
        mergedUserInfo.avatar = DEFAULT_AVATAR
      }
      userStore.setUserInfo(mergedUserInfo)
      console.log('用户详细信息已更新:', mergedUserInfo)
    } else {
      console.warn('获取用户详细信息失败: 返回数据为空')
    }
  } catch (error) {
    console.error('获取用户详细信息失败:', error)
  }
}

// 检查默认页面设置
const checkDefaultPage = () => {
  try {
    // 获取用户设置
    const quickClipAsEnterPage = uni.getStorageSync('quickClipAsEnterPage')
    console.log('快捷剪藏作为入口页面设置:', quickClipAsEnterPage)

    // 如果设置为 true，则跳转到快捷剪藏页面
    if (quickClipAsEnterPage === true) {
      console.log('将快捷剪藏页面设置为默认页面')

      // 使用 switchTab 切换到首页 Tab，然后再使用 navigateTo 跳转到快捷剪藏页面
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index',
          success: () => {
            // 切换到首页后，再跳转到快捷剪藏页面
            setTimeout(() => {
              uni.navigateTo({
                url: '/pages/index/quickclip/quickclip',
                fail: (err) => {
                  console.error('跳转到快捷剪藏页面失败:', err)
                },
              })
            }, 100) // 稍微延迟一下，确保 Tab 切换完成
          },
          fail: (err) => {
            console.error('切换到首页 Tab 失败:', err)
          },
        })
      }, 100) // 稍微延迟一下，确保应用初始化完成
    }
  } catch (error) {
    console.error('检查默认页面设置失败:', error)
  }
}

// 登录函数
const doLogin = async () => {
  const userStore = useUserStore()

  try {
    console.log('执行静默登录')
    const { code } = await uni.login({ provider: 'weixin' })
    console.log('获取到微信登录 code:', code)

    const { data } = await wxLoginApi(code)
    console.log('登录结果:', data)

    // 如果登录成功且返回了用户信息和token
    if (data && typeof data === 'object') {
      // 更新用户信息
      userStore.setUserInfo(data)
      console.log('用户信息已更新:', data)

      // 检查对象中是否包含 token
      const userInfoData = data as IUserInfo
      if (!userInfoData.token) {
        console.warn('服务器返回的用户信息中没有 token')
      }
    } else if (typeof data === 'string') {
      // 如果只返回了token
      userStore.setToken(data)
      console.log('用户Token已更新:', data)
    } else {
      console.warn('登录成功但返回数据格式不符合预期:', data)
    }

    // 检查登录后的状态
    console.log('登录后的状态 - isLogined:', userStore.isLogined)
    console.log('登录后的状态 - token:', userStore.userInfo.token)

    // 登录成功后，异步获取用户详细信息
    if (userStore.isLogined) {
      fetchUserInfo()
    }
  } catch (error) {
    console.error('登录失败:', error)
    // 登录失败时清除可能存在的过期信息
    if (userStore.isLogined) {
      userStore.clearUserInfo()
      console.log('已清除过期的登录信息')
    }
    throw error // 重新抛出错误，让上层处理
  }
}
</script>

<style lang="scss">
/* 设置全局背景色 */
page {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
    '微软雅黑', Arial, sans-serif;
  // background-color: #f8f8f7;
  background-color: #f8f8f7;
}

// stylelint-disable selector-type-no-unknown
button::after {
  border: none;
}

swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略,优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
