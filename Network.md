# 网络请求配置与使用指南

## 1. 网络请求架构概述

本项目使用uni-app框架开发，采用了自定义的HTTP请求封装，主要包含以下几个核心部分：

- **HTTP基础封装**：`src/utils/http.ts`提供了基础的HTTP请求方法
- **请求拦截器**：`src/interceptors/request.ts`处理请求前的参数处理和认证信息
- **请求Hook**：`src/hooks/useRequest.ts`提供了便捷的请求状态管理
- **环境配置**：通过环境变量和平台判断动态设置API地址

## 2. 请求配置

### 2.1 环境变量配置

项目使用Vite的环境变量配置，相关文件位于`env/`目录：

```
# 基础环境变量 (.env)
VITE_SERVER_BASEURL = 'https://api.example.com'  # API基础地址
VITE_UPLOAD_BASEURL = 'https://api.example.com/upload'  # 上传API地址
VITE_APP_PROXY = false  # 是否启用代理（H5环境）
VITE_APP_PROXY_PREFIX = '/api'  # 代理前缀
```

### 2.2 环境区分

项目会根据不同环境自动选择对应的API地址：

```typescript
// 根据微信小程序当前环境，判断应该获取的BaseUrl
export const getEnvBaseUrl = () => {
  // 请求基准地址
  let baseUrl = import.meta.env.VITE_SERVER_BASEURL

  // 小程序端环境区分
  if (isMp) {
    const {
      miniProgram: { envVersion },
    } = uni.getAccountInfoSync()

    switch (envVersion) {
      case 'develop':
        baseUrl = 'http://localhost:8080'
        break
      case 'trial':
        baseUrl = 'https://localhost:8080'
        break
      case 'release':
        baseUrl = 'https://www.notionmpclipper.site/notionclipper'
        break
    }
  }

  return baseUrl
}
```

## 3. 请求方法

### 3.1 基础HTTP方法

项目提供了以下基础HTTP方法：

```typescript
// GET请求
httpGet<T>(url: string, query?: Record<string, any>)

// POST请求
httpPost<T>(url: string, data?: Record<string, any>, query?: Record<string, any>)
```

### 3.2 使用示例

```typescript
import { httpGet, httpPost } from '@/utils/http'

// GET请求示例
const fetchData = async () => {
  try {
    const res = await httpGet<UserInfo>('/api/user', { id: 1 })
    // res.data 包含返回的数据
    // res.code 包含状态码
    // res.msg 包含消息
  } catch (error) {
    // 错误处理
  }
}

// POST请求示例
const submitData = async () => {
  try {
    const res = await httpPost<SubmitResult>('/api/submit', { name: '测试' })
    // 处理返回结果
  } catch (error) {
    // 错误处理
  }
}
```

## 4. 请求拦截器

### 4.1 请求前处理

请求拦截器会在发送请求前进行以下处理：

1. **Query参数处理**：将query对象转换为URL查询字符串
2. **基础URL拼接**：自动拼接API基础地址
3. **超时设置**：默认10秒超时
4. **平台标识**：添加平台标识到请求头
5. **认证信息**：自动添加token到Authorization请求头

```typescript
const httpInterceptor = {
  invoke(options: CustomRequestOptions) {
    // Query参数处理
    if (options.query) {
      const queryStr = qs.stringify(options.query)
      if (options.url.includes('?')) {
        options.url += `&${queryStr}`
      } else {
        options.url += `?${queryStr}`
      }
    }

    // 非http开头需拼接地址
    if (!options.url.startsWith('http')) {
      options.url = baseUrl + options.url
    }

    // 请求超时
    options.timeout = 10000 // 10s

    // 添加平台标识
    options.header = {
      platform, // 平台标识
      ...options.header,
    }

    // 添加token
    const userStore = useUserStore()
    const { token } = userStore.userInfo as unknown as IUserInfo
    if (token) {
      options.header.Authorization = `Bearer ${token}`
    }
  },
}
```

## 5. 响应处理

### 5.1 响应成功处理

响应处理逻辑如下：

1. **状态码处理**：

   - 2xx：正常返回数据
   - 401：未授权，可以在这里处理登录跳转
   - 其他错误：显示错误提示

2. **数据结构**：所有接口返回统一的数据结构

```typescript
type IResData<T> = {
  code: number // 业务状态码
  msg: string // 消息
  data: T // 实际数据
  success: boolean // 是否成功
  requestId: string
}
```

### 5.2 错误处理

```typescript
// 响应成功但状态码非2xx
if (!(res.statusCode >= 200 && res.statusCode < 300)) {
  if (res.statusCode === 401) {
    // 401错误 -> 清理用户信息，跳转到登录页
    // userStore.clearUserInfo()
    // uni.navigateTo({ url: '/pages/login/login' })
  } else {
    // 其他错误 -> 根据后端错误信息轻提示
    !options.hideErrorToast &&
      uni.showToast({
        icon: 'none',
        title: (res.data as IResData<T>).msg || '请求错误',
      })
  }
}

// 网络错误
fail(err) {
  uni.showToast({
    icon: 'none',
    title: '网络错误，换个网络试试',
  })
  reject(err)
}
```

## 6. useRequest Hook

项目提供了`useRequest` Hook来简化请求状态管理：

```typescript
import useRequest from '@/hooks/useRequest'

// 在组件中使用
const fetchUserData = () => httpGet<UserInfo>('/api/user')
const { loading, error, data, run } = useRequest(fetchUserData, {
  immediate: true, // 是否立即执行
  initialData: {}, // 初始数据
})

// 手动触发请求
const handleRefresh = () => {
  run()
}

// 在模板中使用
// <template>
//   <view v-if="loading">加载中...</view>
//   <view v-else-if="error">加载失败</view>
//   <view v-else>{{ data }}</view>
// </template>
```

## 7. 文件上传

项目支持文件上传，使用与普通请求相同的拦截器：

```typescript
// 拦截器同时应用于请求和文件上传
uni.addInterceptor('request', httpInterceptor)
uni.addInterceptor('uploadFile', httpInterceptor)
```

上传文件类型定义：

```typescript
type IUniUploadFileOptions = {
  file?: File
  files?: UniApp.UploadFileOptionFiles[]
  filePath?: string
  name?: string
  formData?: any
}
```

## 8. 最佳实践

### 8.1 API模块化

建议将API请求按模块组织，例如：

```typescript
// src/api/user.ts
import { httpGet, httpPost } from '@/utils/http'

export const getUserInfo = () => httpGet<UserInfo>('/api/user')
export const updateUserInfo = (data: UserUpdateParams) =>
  httpPost<boolean>('/api/user/update', data)

// 使用
import { getUserInfo } from '@/api/user'

const { data, loading, run } = useRequest(getUserInfo, { immediate: true })
```

### 8.2 错误处理

对于需要特殊处理的错误，可以在catch中处理：

```typescript
try {
  const res = await httpGet<UserInfo>('/api/user')
  // 处理成功响应
} catch (error) {
  // 可以根据error.statusCode或其他信息进行特殊处理
  if (error.statusCode === 403) {
    // 处理权限不足
  } else {
    // 其他错误处理
  }
}
```

### 8.3 隐藏错误提示

如果不希望显示默认的错误提示，可以设置`hideErrorToast`参数：

```typescript
httpGet<UserInfo>('/api/user', {}, { hideErrorToast: true })
```

## 9. 类型定义

项目中的主要类型定义：

```typescript
// 响应数据结构
type IResData<T> = {
  code: number
  msg: string
  data: T
}

// 请求选项扩展
type CustomRequestOptions = UniApp.RequestOptions & {
  query?: Record<string, any>
  hideErrorToast?: boolean
} & IUniUploadFileOptions

// 用户信息类型
type IUserInfo = {
  nickname?: string
  avatar?: string
  openid?: string
  token?: string
}
```
