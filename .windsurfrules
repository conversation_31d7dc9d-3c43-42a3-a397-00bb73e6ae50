本项目使用uni-app开发，并使用了 wot-design-uni 组件库。
本项目使用 pinia 进行状态管理
本项目使用 z-paging 进行分页
本项目使用了 unocss 进行样式管理

在设计页面时，尽可能与本项目其他页面的配色保持一致。

在写代码时，需要参考 wot-design-uni 的文档和示例。尽可能使用 wot-design-uni 的组件，而不是自定义组件。
如果需要修改样式，尽可能使用 wot-design-uni 的自定义样式类，而不是直接修改组件的样式。
在写代码时，尽可能使用 unocss 的原子类，而不是传统的写 css。当然如果原子类写起来比较冗长，则酌情处理，但需要告知我。
在写代码时，请先查看项目的 eslint 配置，然后根据 eslint 的规则来写代码。

当需要用到交互反馈时，需要使用 wot-design-uni 的 toast 组件，而不是直接使用 uni 的 toast。
需要使用确认对话框时，需要使用 wot-design-uni 的 MessageBox 组件，而不是直接使用 uni 的 modal。