# 📋 Notion剪藏助手微信小程序

<div align="center">
  <p>
    <b>一站式Notion内容收集与管理解决方案</b>
  </p>
  <p>
    <img src="https://img.shields.io/badge/框架-uni--app-green" alt="uni-app">
    <img src="https://img.shields.io/badge/UI-wot--design--uni-blue" alt="wot-design-uni">
    <img src="https://img.shields.io/badge/语言-TypeScript-blue" alt="TypeScript">
  </p>
</div>

## 📝 项目介绍

Notion剪藏助手微信小程序是一款专为Notion用户设计的内容收集工具，支持将各类内容（文字、图片、链接、文件、聊天记录等）快速保存到Notion中。通过微信小程序和收藏助手的配合使用，用户可以方便地将日常浏览到的有价值内容进行收集和整理。

### 🌟 项目特点

- **多平台支持**：支持小红书、即刻、少数派、下厨房、豆瓣等多个平台内容的剪藏
- **多类型内容**：支持文字、图片、链接、文件、聊天记录等多种内容类型
- **标签管理**：智能标签系统，支持快速分类和检索
- **自定义配置**：灵活的图床配置和存储选项
- **用户友好**：简洁直观的界面设计，操作便捷

## 📋 待办清单

- [ ] 完善收藏助手的绑定流程实际功能实现
- [ ] 实现图床配置的保存和读取功能
- [ ] 实现增强功能的各平台配置保存和读取
- [ ] 实现会员系统的完整功能
- [ ] 优化首页UI和交互体验
- [ ] 添加使用教程和帮助文档
- [ ] 实现剪藏功能的实际同步逻辑
- [ ] 添加更多支持的平台和内容类型
- [ ] 完善错误处理和用户反馈机制
- [ ] 实现数据统计和分析功能

## 主要功能

### 基础功能

- **内容收集**：支持文字、图片、链接等基础内容的收集
- **标签管理**：支持在文字末尾通过#号添加标签
- **平台转发**：支持外部平台内容直接转发到Notion

### 增强功能

- **文件保存**：支持保存各类文件到Notion
- **聊天记录**：支持保存聊天记录内容
- **多媒体内容**：支持保存语音、视频等多媒体内容

### 图床支持

支持多种图床平台配置：

- 七牛云
- 阿里云OSS
- 腾讯云COS
- MinIO
- 兼容S3的存储服务
- 缤纷云
- Cloudflare R2
- Cloudinary

## 技术栈

- **框架**：uni-app
- **UI组件**：wot-design-uni
- **状态管理**：Pinia
- **分页组件**：z-paging
- **样式管理**：unocss
- **语言**：TypeScript
- **构建工具**：Vite

## 项目结构

```
├── src                     # 源代码
│   ├── components          # 公共组件
│   ├── hooks               # 自定义hooks
│   ├── interceptors        # 拦截器
│   ├── layouts             # 布局组件
│   ├── pages               # 页面
│   │   ├── about           # 关于页面及相关功能
│   │   ├── index           # 首页
│   │   └── operate         # 操作相关页面
│   ├── pages-sub           # 子页面
│   ├── service             # 服务层
│   ├── store               # 状态管理
│   ├── style               # 样式文件
│   ├── types               # 类型定义
│   └── utils               # 工具函数
├── static                  # 静态资源
└── uni_modules             # uni-app模块
```

## 📷 屏幕截图

> 注意：以下截图仅作示例，实际界面可能会有所不同。

<div align="center">
  <p>屏幕截图将在项目完善后添加</p>
</div>

## 使用方法

### 开发环境

```bash
# 安装依赖
pnpm install

# 开发模式运行
pnpm dev:mp-weixin  # 微信小程序
pnpm dev:h5         # H5版本

# 构建
pnpm build:mp-weixin  # 微信小程序
pnpm build:h5         # H5版本
```

### 配置说明

1. **收藏助手配置**：

   - 添加收藏助手为好友
   - 生成并发送绑定口令
   - 发送测试消息验证同步

2. **图床配置**：

   - 选择并配置图床平台
   - 填写相关密钥和配置信息
   - 保存配置后即可使用

3. **增强功能配置**：
   - 绑定收藏助手后开启增强功能
   - 选择并配置存储平台
   - 填写相关配置信息并保存

## 🎨 主色方案

本项目采用了以下主色方案，以提供一致的用户体验：

- **主色调**：海洋蓝 (#337ea9) - 传递可靠、专业的形象
- **辅助色**：
  - 浅绿色 (#E7F3E8/#448361) - 表示成功和成长
  - 浅红色 (#FCE8E9/#D44C47) - 表示重要和激励
  - 浅黄色 (#FAF1D8/#CB912F) - 表示警告和提示
  - 浅灰色 (#F0F0F0/#666666) - 表示中性和辅助

该配色方案结合了专业性和友好性，同时保持了视觉上的和谐与平衡。每种颜色都有其背景色和文字色的组合，确保在不同场景下的可读性。

## 💪 会员系统

本项目包含会员系统，提供以下会员类型：

- **普通用户**：基础功能，包括基本的内容剪藏和标签管理
- **年卡会员**：全部功能，包括增强功能和图床支持，有效期一年
- **永久会员**：全部功能，永久有效，享受所有未来更新

## ❓ 常见问题

### Q: 如何绑定我的Notion账户？

A: 在小程序中进入“我的”页面，点击“绑定Notion”，按照指引完成授权流程。

### Q: 支持哪些图床服务？

A: 支持七牛云、阿里云OSS、腾讯云COS、MinIO、S3兼容服务、缤纷云、Cloudflare R2和Cloudinary等多种图床服务。

### Q: 如何将小红书内容剪藏到Notion？

A: 在小红书App中点击分享按钮，选择“复制链接”，然后在小程序中粘贴链接并点击剪藏。
